{"interfaces": {"google.cloud.speech.v2.Speech": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"CreateRecognizer": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "ListRecognizers": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "GetRecognizer": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "UpdateRecognizer": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "DeleteRecognizer": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "UndeleteRecognizer": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "Recognize": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "StreamingRecognize": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "BatchRecognize": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "GetConfig": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "UpdateConfig": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "CreateCustomClass": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "ListCustomClasses": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "GetCustomClass": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "UpdateCustomClass": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "DeleteCustomClass": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "UndeleteCustomClass": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "CreatePhraseSet": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "ListPhraseSets": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "GetPhraseSet": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "UpdatePhraseSet": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "DeletePhraseSet": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "UndeletePhraseSet": {"timeout_millis": 5000000, "retry_codes_name": "idempotent", "retry_params_name": "default"}}}}}