"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const payment_controller_1 = require("./payment.controller");
const payment_service_1 = require("./payment.service");
const payment_entity_1 = require("./entities/payment.entity");
const coupon_usage_entity_1 = require("./entities/coupon-usage.entity");
const coupon_module_1 = require("../coupon/coupon.module");
const subscription_module_1 = require("../subscription/subscription.module");
const user_module_1 = require("../user/user.module");
const alipay_processor_1 = require("./processors/alipay.processor");
const wechat_processor_1 = require("./processors/wechat.processor");
const stripe_processor_1 = require("./processors/stripe.processor");
let PaymentModule = class PaymentModule {
};
exports.PaymentModule = PaymentModule;
exports.PaymentModule = PaymentModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([payment_entity_1.Payment, coupon_usage_entity_1.CouponUsage]),
            coupon_module_1.CouponModule,
            subscription_module_1.SubscriptionModule,
            user_module_1.UserModule,
        ],
        controllers: [payment_controller_1.PaymentController],
        providers: [
            payment_service_1.PaymentService,
            alipay_processor_1.AlipayProcessor,
            wechat_processor_1.WechatProcessor,
            stripe_processor_1.StripeProcessor,
        ],
        exports: [payment_service_1.PaymentService],
    })
], PaymentModule);
//# sourceMappingURL=payment.module.js.map