import { io, Socket } from 'socket.io-client';

interface TranslationResult {
  id: string;
  originalText: string;
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  timestamp: Date;
  isQuestion: boolean;
}

interface QuestionAnswer {
  id: string;
  question: string;
  answer: string;
  timestamp: Date;
}

interface SpeechRecognitionResult {
  text: string;
  language: string;
  timestamp: Date;
}

class SocketService {
  private socket: Socket | null = null;
  private isConnected = false;

  connect(token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.socket = io('http://localhost:3000/translation', {
          auth: { token },
          transports: ['websocket'],
          forceNew: true,
        });

        this.socket.on('connect', () => {
          console.log('Socket connected');
          this.isConnected = true;
          resolve();
        });

        this.socket.on('authenticated', (data) => {
          console.log('Socket authenticated:', data);
        });

        this.socket.on('connect_error', (error) => {
          console.error('Socket connection error:', error);
          this.isConnected = false;
          reject(error);
        });

        this.socket.on('error', (error) => {
          console.error('Socket error:', error);
        });

        this.socket.on('disconnect', () => {
          console.log('Socket disconnected');
          this.isConnected = false;
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  // 发送翻译请求
  translate(text: string, sourceLanguage: string, targetLanguage: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('translate', {
        text,
        sourceLanguage,
        targetLanguage,
      });
    } else {
      console.error('Socket not connected');
    }
  }

  // 发送语音流
  sendVoiceStream(audioData: string, language: string, sessionId?: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('voice_stream', {
        audioData,
        language,
        sessionId,
      });
    } else {
      console.error('Socket not connected');
    }
  }

  // 获取翻译历史
  getTranslationHistory(limit?: number) {
    if (this.socket && this.isConnected) {
      this.socket.emit('get_translation_history', { limit });
    } else {
      console.error('Socket not connected');
    }
  }

  // 监听翻译结果
  onTranslationResult(callback: (result: TranslationResult) => void) {
    if (this.socket) {
      this.socket.on('translation_result', callback);
    }
  }

  // 监听问答结果
  onQuestionAnswer(callback: (answer: QuestionAnswer) => void) {
    if (this.socket) {
      this.socket.on('question_answer', callback);
    }
  }

  // 监听语音识别结果
  onSpeechRecognitionResult(callback: (result: SpeechRecognitionResult) => void) {
    if (this.socket) {
      this.socket.on('speech_recognition_result', callback);
    }
  }

  // 监听翻译历史
  onTranslationHistory(callback: (history: any[]) => void) {
    if (this.socket) {
      this.socket.on('translation_history', callback);
    }
  }

  // 监听语音处理开始
  onVoiceProcessingStart(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('voice_processing_start', callback);
    }
  }

  // 监听语音处理完成
  onVoiceProcessingComplete(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('voice_processing_complete', callback);
    }
  }

  // 监听服务器状态
  onServerStatus(callback: (status: any) => void) {
    if (this.socket) {
      this.socket.on('server_status', callback);
    }
  }

  // 监听认证成功
  onAuthenticated(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('authenticated', callback);
    }
  }

  // 移除所有监听器
  removeAllListeners() {
    if (this.socket) {
      this.socket.removeAllListeners();
    }
  }

  // 移除特定事件监听器
  off(event: string, callback?: (...args: any[]) => void) {
    if (this.socket) {
      this.socket.off(event, callback);
    }
  }
}

export const socketService = new SocketService();
export type { TranslationResult, QuestionAnswer, SpeechRecognitionResult };
