import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Mic<PERSON>ff, Volume2, VolumeX, Settings, Play, HelpCircle, MessageCircle, Wifi, WifiOff, RefreshCw } from 'lucide-react';
import { useSpeechRecognition } from '../hooks/useVoiceRecording';
import { RealtimeTranslator, type TranslationResult } from '../services/realtimeTranslator';

interface TranslationEntry {
  id: string;
  original: string;
  translated: string;
  timestamp: Date;
  isQuestion: boolean;
  aiAnswer?: string;
  confidence?: number;
  isFinal?: boolean;
}

const TranslationInterface: React.FC = () => {
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('zh-CN');
  const [targetLanguage, setTargetLanguage] = useState('en-US');
  const [translationHistory, setTranslationHistory] = useState<TranslationEntry[]>([]);
  const [isQuestionDetected, setIsQuestionDetected] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState('');
  const [voiceError, setVoiceError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStatus, setProcessingStatus] = useState<string>('');

  // 性能统计
  const [translationCount, setTranslationCount] = useState(0);
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null);
  const [averageLatency, setAverageLatency] = useState(0);

  const translatorRef = useRef<RealtimeTranslator | null>(null);

  // 初始化翻译器
  useEffect(() => {
    const translator = new RealtimeTranslator(
      (result: TranslationResult) => {
        // 处理翻译结果
        const entry: TranslationEntry = {
          id: result.id,
          original: result.originalText,
          translated: result.translatedText,
          timestamp: result.timestamp,
          isQuestion: result.isQuestion || false,
          aiAnswer: result.aiAnswer,
          confidence: result.confidence,
          isFinal: result.isFinal
        };

        setTranslationHistory(prev => {
          // 如果是中间结果，替换最后一个非最终结果
          if (!result.isFinal) {
            // 查找最后一个非最终结果进行替换
            const lastNonFinalIndex = prev.length - 1;
            if (lastNonFinalIndex >= 0 && !prev[lastNonFinalIndex].isFinal) {
              const newHistory = [...prev];
              newHistory[lastNonFinalIndex] = entry;
              return newHistory;
            }
          }
          // 添加新条目，但限制历史记录数量
          return [entry, ...prev.slice(0, 19)]; // 保持最新的20条记录
        });

        setTranslationCount(prev => prev + 1);
        setIsQuestionDetected(result.isQuestion || false);
        if (result.isQuestion) {
          setCurrentQuestion(result.originalText);
        }
      },
      (error: string) => {
        setVoiceError(error);
        setIsProcessing(false);
      }
    );

    translator.setLanguages(currentLanguage, targetLanguage);
    translatorRef.current = translator;

    return () => {
      translatorRef.current = null;
    };
  }, [currentLanguage, targetLanguage]);

  // 处理语音识别结果
  const handleSpeechResult = async (text: string, confidence: number, isFinal: boolean) => {
    if (!translatorRef.current) return;

    try {
      setIsProcessing(true);
      setProcessingStatus(isFinal ? '生成最终翻译...' : '实时翻译中...');

      // 调用翻译器
      await translatorRef.current.translateText(text, confidence, isFinal);

      if (isFinal) {
        setIsProcessing(false);
        setProcessingStatus('');
      }
    } catch (error) {
      console.error('翻译处理失败:', error);
      setVoiceError('翻译处理失败');
      setIsProcessing(false);
    }
  };

  // 处理语音错误
  const handleVoiceError = (error: string) => {
    setVoiceError(error);
    setIsListening(false);
  };

  // 使用语音识别 Hook
  const {
    isRecording,
    startRecording,
    stopRecording,
    audioLevel,
    error: recordingError
  } = useSpeechRecognition(handleSpeechResult, handleVoiceError, currentLanguage);

  // 会话管理
  useEffect(() => {
    if (isListening && !sessionStartTime) {
      setSessionStartTime(new Date());
    } else if (!isListening && sessionStartTime) {
      setSessionStartTime(null);
    }
  }, [isListening, sessionStartTime]);




  // 同步录音状态
  useEffect(() => {
    setIsListening(isRecording);
  }, [isRecording]);

  // 语言切换
  const swapLanguages = () => {
    if (translatorRef.current) {
      translatorRef.current.swapLanguages();
    }
    const temp = currentLanguage;
    setCurrentLanguage(targetLanguage);
    setTargetLanguage(temp);
  };

  // 清空历史记录
  const clearHistory = () => {
    setTranslationHistory([]);
    setTranslationCount(0);
    if (translatorRef.current) {
      translatorRef.current.clearCache();
    }
  };

  return (
    <div className="space-y-6">

      {/* 语音错误提示 */}
      {(voiceError || recordingError) && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4">
          <div className="flex items-center space-x-3">
            <MicOff className="w-5 h-5 text-red-500" />
            <p className="text-red-700 text-sm">{voiceError || recordingError}</p>
            <button
              onClick={() => {
                setVoiceError(null);
              }}
              className="ml-auto text-red-500 hover:text-red-700"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* 性能统计面板 */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-3">
            <div className="text-blue-600 text-sm font-medium">翻译次数</div>
            <div className="text-2xl font-bold text-blue-700">{translationCount}</div>
          </div>
          <div className="bg-green-50 rounded-lg p-3">
            <div className="text-green-600 text-sm font-medium">会话时长</div>
            <div className="text-2xl font-bold text-green-700">
              {sessionStartTime ? Math.floor((Date.now() - sessionStartTime.getTime()) / 1000) : 0}s
            </div>
          </div>
          <div className="bg-purple-50 rounded-lg p-3">
            <div className="text-purple-600 text-sm font-medium">缓存命中</div>
            <div className="text-2xl font-bold text-purple-700">
              {translatorRef.current?.getCacheStats().size || 0}
            </div>
          </div>
          <div className="bg-orange-50 rounded-lg p-3">
            <div className="text-orange-600 text-sm font-medium">状态</div>
            <div className="text-sm font-bold text-orange-700">
              {isListening ? '🎤 监听中' : '⏸️ 待机'}
            </div>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex items-center space-x-4">
            <button
              onClick={async () => {
                if (isRecording) {
                  await stopRecording();
                  setIsListening(false);
                } else {
                  setVoiceError(null);
                  setIsListening(true);
                  await startRecording();
                }
              }}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all ${
                isRecording
                  ? 'bg-red-500 hover:bg-red-600 text-white shadow-lg animate-pulse'
                  : 'bg-blue-500 hover:bg-blue-600 text-white shadow-md hover:shadow-lg'
              }`}
            >
              {isRecording ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
              <span>{isRecording ? '停止录音' : '开始录音'}</span>
              {isRecording && (
                <div className="w-2 h-2 bg-white rounded-full animate-pulse ml-1" />
              )}
            </button>

            {/* 音量指示器和处理状态 */}
            {isRecording && (
              <div className="flex items-center space-x-2 px-3 py-2 bg-red-50 border border-red-200 rounded-lg">
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                <span className="text-sm text-red-700">录音中</span>
                <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-red-500 transition-all duration-100"
                    style={{ width: `${audioLevel * 100}%` }}
                  />
                </div>
              </div>
            )}

            {/* 处理状态指示器 */}
            {isProcessing && (
              <div className="flex items-center space-x-2 px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                <span className="text-sm text-blue-700">{processingStatus || '正在处理...'}</span>
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
              </div>
            )}

            <button
              onClick={() => setIsSpeaking(!isSpeaking)}
              className={`flex items-center space-x-2 px-4 py-3 rounded-lg border transition-colors ${
                isSpeaking
                  ? 'bg-green-50 border-green-300 text-green-700'
                  : 'bg-gray-50 border-gray-300 text-gray-600 hover:bg-gray-100'
              }`}
            >
              {isSpeaking ? <Volume2 className="w-5 h-5" /> : <VolumeX className="w-5 h-5" />}
              <span>{isSpeaking ? '语音播放开启' : '语音播放关闭'}</span>
            </button>
          </div>
          
          <div className="flex items-center space-x-4">
            <select
              value={currentLanguage}
              onChange={(e) => setCurrentLanguage(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg bg-white"
            >
              <option value="zh-CN">中文</option>
              <option value="en-US">English</option>
              <option value="ja-JP">日本語</option>
              <option value="ko-KR">한국어</option>
            </select>

            <button
              onClick={swapLanguages}
              className="p-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 transition-colors"
              title="切换语言"
            >
              <RefreshCw className="w-4 h-4 text-gray-600" />
            </button>

            <span className="text-gray-500">→</span>
            
            <select
              value={targetLanguage}
              onChange={(e) => setTargetLanguage(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg bg-white"
            >
              <option value="en-US">English</option>
              <option value="zh-CN">中文</option>
              <option value="ja-JP">日本語</option>
              <option value="ko-KR">한국어</option>
            </select>

            <button
              onClick={clearHistory}
              className="px-4 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 transition-colors text-sm"
              title="清空历史记录"
            >
              清空历史
            </button>
          </div>
        </div>
      </div>

      {/* 问题检测提示 */}
      {isQuestionDetected && (
        <div className="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-xl p-4 animate-pulse">
          <div className="flex items-center space-x-3">
            <HelpCircle className="w-6 h-6 text-orange-500" />
            <div>
              <h3 className="font-semibold text-orange-800">检测到提问！</h3>
              <p className="text-orange-700 text-sm">AI正在分析问题并准备回答...</p>
            </div>
          </div>
        </div>
      )}

      {/* 大字体实时翻译显示 */}
      {translationHistory.length > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-lg p-6 border-2 border-blue-200">
          <div className="text-center">
            <h2 className="text-lg font-semibold text-blue-800 mb-4">🎯 实时翻译</h2>
            <div className="space-y-4">
              {/* 最新的原文 */}
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="text-sm text-gray-500 mb-2">原文 ({currentLanguage === 'zh-CN' ? '中文' : 'English'})</div>
                <div className="text-xl font-medium text-gray-800">
                  {translationHistory[0]?.original || '等待语音输入...'}
                </div>
              </div>

              {/* 最新的翻译 */}
              <div className="bg-blue-600 text-white rounded-lg p-4 shadow-md">
                <div className="text-sm text-blue-100 mb-2">翻译 ({targetLanguage === 'zh-CN' ? '中文' : 'English'})</div>
                <div className="text-2xl font-bold">
                  {translationHistory[0]?.translated || '翻译结果将在这里显示...'}
                </div>
                {translationHistory[0] && !translationHistory[0].isFinal && (
                  <div className="text-sm text-blue-200 mt-2 flex items-center">
                    <div className="w-2 h-2 bg-blue-200 rounded-full animate-pulse mr-2" />
                    实时翻译中...
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 实时翻译显示 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 翻译历史 */}
        <div className="bg-white rounded-xl shadow-lg">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">实时翻译</h2>
          </div>
          <div className="p-4 space-y-4 max-h-96 overflow-y-auto">
            {translationHistory.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                <Mic className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>点击"开始监听"开始实时翻译</p>
              </div>
            ) : (
              translationHistory.map((entry) => (
                <div key={entry.id} className={`p-3 rounded-lg border-l-4 ${
                  entry.isQuestion ? 'border-orange-400 bg-orange-50' :
                  entry.isFinal ? 'border-green-400 bg-green-50' : 'border-blue-400 bg-blue-50'
                }`}>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">
                          {entry.timestamp.toLocaleTimeString()}
                        </span>
                        {entry.isQuestion && (
                          <span className="inline-flex items-center px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full">
                            <HelpCircle className="w-3 h-3 mr-1" />
                            提问
                          </span>
                        )}
                        {!entry.isFinal && (
                          <span className="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                            ⚡ 实时
                          </span>
                        )}
                      </div>
                      {entry.confidence && (
                        <span className="text-xs text-gray-500">
                          置信度: {Math.round(entry.confidence * 100)}%
                        </span>
                      )}
                    </div>
                    <p className="text-gray-900 font-medium">{entry.original}</p>
                    <p className="text-gray-700 text-sm">{entry.translated}</p>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* AI 问答 */}
        <div className="bg-white rounded-xl shadow-lg">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">AI 智能问答</h2>
          </div>
          <div className="p-4 space-y-4 max-h-96 overflow-y-auto">
            {translationHistory.filter(entry => entry.isQuestion && entry.aiAnswer).length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                <HelpCircle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>当检测到提问时，AI将自动生成回答</p>
              </div>
            ) : (
              translationHistory
                .filter(entry => entry.isQuestion && entry.aiAnswer)
                .map((entry) => (
                  <div key={`answer-${entry.id}`} className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <HelpCircle className="w-4 h-4 text-blue-600" />
                        <span className="font-medium text-blue-900">问题:</span>
                      </div>
                      <p className="text-gray-800 text-sm ml-6">{entry.original}</p>
                      
                      <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
                        <span className="font-medium text-purple-900">AI 回答:</span>
                      </div>
                      <p className="text-gray-800 text-sm ml-6 bg-white p-3 rounded-lg border">{entry.aiAnswer}</p>
                    </div>
                  </div>
                ))
            )}
          </div>
        </div>
      </div>

      {/* 统计面板 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg p-4 shadow-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">今日翻译</p>
              <p className="text-2xl font-bold text-blue-600">1,234</p>
            </div>
            <MessageCircle className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg p-4 shadow-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">检测提问</p>
              <p className="text-2xl font-bold text-orange-600">87</p>
            </div>
            <HelpCircle className="w-8 h-8 text-orange-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg p-4 shadow-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">在线时长</p>
              <p className="text-2xl font-bold text-green-600">3.5h</p>
            </div>
            <Play className="w-8 h-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg p-4 shadow-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">语言对数</p>
              <p className="text-2xl font-bold text-purple-600">12</p>
            </div>
            <Settings className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TranslationInterface;
