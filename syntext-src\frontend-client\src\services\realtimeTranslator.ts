export interface TranslationResult {
  id: string;
  originalText: string;
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  confidence: number;
  isFinal: boolean;
  timestamp: Date;
  isQuestion?: boolean;
  aiAnswer?: string;
}

export class RealtimeTranslator {
  private translationCache = new Map<string, string>();
  private sourceLanguage: string = 'zh-CN';
  private targetLanguage: string = 'en-US';
  private deepseekApiKey: string = '***********************************';
  private deepseekApiUrl: string = 'https://api.deepseek.com/v1';

  constructor(
    private onTranslationResult: (result: TranslationResult) => void,
    private onError: (error: string) => void
  ) {}

  // 设置语言
  setLanguages(source: string, target: string) {
    this.sourceLanguage = source;
    this.targetLanguage = target;
  }

  // 交换语言
  swapLanguages() {
    const temp = this.sourceLanguage;
    this.sourceLanguage = this.targetLanguage;
    this.targetLanguage = temp;
    
    // 清空缓存（翻译方向改变）
    this.translationCache.clear();
  }

  // 翻译文本
  async translateText(text: string, confidence: number, isFinal: boolean = false) {
    const startTime = performance.now();
    
    try {
      // 检查缓存
      const cacheKey = `${this.sourceLanguage}->${this.targetLanguage}:${text.toLowerCase().trim()}`;
      if (this.translationCache.has(cacheKey)) {
        const cachedTranslation = this.translationCache.get(cacheKey)!;
        this.emitTranslationResult(text, cachedTranslation, confidence, isFinal, true);
        return;
      }

      // 调用API翻译
      const translation = await this.callTranslationAPI(text);
      
      // 缓存最终结果
      if (isFinal) {
        this.translationCache.set(cacheKey, translation);
      }

      // 检测是否为问题并生成AI回答
      let aiAnswer: string | undefined;
      if (isFinal && this.isQuestion(text)) {
        try {
          aiAnswer = await this.generateAIAnswer(text);
        } catch (error) {
          console.warn('AI回答生成失败:', error);
        }
      }

      this.emitTranslationResult(text, translation, confidence, isFinal, false, aiAnswer);
      
    } catch (error) {
      console.error('翻译失败:', error);
      this.onError(`翻译失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 调用DeepSeek翻译API
  private async callTranslationAPI(text: string): Promise<string> {
    // 动态系统提示词 - 极简版本
    let systemPrompt: string;
    if (this.sourceLanguage === 'en-US' && this.targetLanguage === 'zh-CN') {
      systemPrompt = '直接翻译成中文，只输出翻译结果：';
    } else if (this.sourceLanguage === 'zh-CN' && this.targetLanguage === 'en-US') {
      systemPrompt = '直接翻译成英文，只输出翻译结果：';
    } else {
      systemPrompt = `直接翻译成${this.getLanguageName(this.targetLanguage)}，只输出翻译结果：`;
    }

    const response = await fetch(`${this.deepseekApiUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.deepseekApiKey}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          { role: 'user', content: `${systemPrompt}\n\n${text}` }
        ],
        max_tokens: 200,  // 减少token数量提高速度
        temperature: 0.05  // 更低温度保证简洁输出
      })
    });

    if (!response.ok) {
      throw new Error(`DeepSeek API错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const translation = data.choices?.[0]?.message?.content?.trim();

    if (!translation) {
      throw new Error('翻译结果为空');
    }

    // 过滤掉提示词内容，只返回纯翻译结果
    const cleanTranslation = this.cleanTranslationResult(translation);
    return cleanTranslation;
  }

  // 检测是否为问题
  private isQuestion(text: string): boolean {
    const questionMarkers = {
      chinese: ['？', '什么', '怎么', '为什么', '如何', '谁', '哪里', '什么时候', '哪个', '多少', '几', '能否', '可以吗', '是否'],
      english: ['?', 'what', 'how', 'why', 'who', 'where', 'when', 'which', 'can', 'could', 'would', 'should', 'is', 'are', 'do', 'does', 'did']
    };

    const lowerText = text.toLowerCase();
    const hasQuestionMark = text.includes('？') || text.includes('?');
    const hasQuestionWords = [
      ...questionMarkers.chinese,
      ...questionMarkers.english
    ].some(marker => lowerText.includes(marker.toLowerCase()));

    return hasQuestionMark || hasQuestionWords;
  }

  // 生成AI回答
  private async generateAIAnswer(question: string): Promise<string> {
    const isChineseQuestion = /[\u4e00-\u9fa5]/.test(question);
    
    const systemPrompt = isChineseQuestion 
      ? '你是一个知识渊博、友善的AI助手。请根据用户的问题提供准确、有用、简洁的中文回答。回答要准确可靠、简洁明了、友善礼貌。如果不确定答案，请诚实说明。'
      : 'You are a knowledgeable and friendly AI assistant. Please provide accurate, helpful, and concise answers to user questions. Your answers should be reliable, clear, and polite. If you are unsure about an answer, please be honest about it.';

    const userPrompt = isChineseQuestion 
      ? `请回答以下问题：${question}`
      : `Please answer the following question: ${question}`;

    const response = await fetch(`${this.deepseekApiUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.deepseekApiKey}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: 500,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      throw new Error(`AI回答API错误: ${response.status}`);
    }

    const data = await response.json();
    const answer = data.choices?.[0]?.message?.content?.trim();

    if (!answer) {
      throw new Error('AI回答为空');
    }

    return answer;
  }

  // 发送翻译结果
  private emitTranslationResult(
    originalText: string, 
    translatedText: string, 
    confidence: number, 
    isFinal: boolean, 
    fromCache: boolean = false,
    aiAnswer?: string
  ) {
    const result: TranslationResult = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      originalText,
      translatedText,
      sourceLanguage: this.sourceLanguage,
      targetLanguage: this.targetLanguage,
      confidence,
      isFinal,
      timestamp: new Date(),
      isQuestion: this.isQuestion(originalText),
      aiAnswer
    };

    this.onTranslationResult(result);
  }

  // 获取语言名称
  private getLanguageName(languageCode: string): string {
    const languageNames: { [key: string]: string } = {
      'zh-CN': '中文',
      'en-US': '英文',
      'ja-JP': '日文',
      'ko-KR': '韩文'
    };
    return languageNames[languageCode] || languageCode;
  }

  // 清空缓存
  clearCache() {
    this.translationCache.clear();
  }

  // 清理翻译结果，移除提示词内容
  private cleanTranslationResult(translation: string): string {
    // 移除常见的提示词回复模式
    const cleanPatterns = [
      /^You are a.*?translator.*?\./i,
      /^Please translate.*?\./i,
      /^I am a.*?translator.*?\./i,
      /^As a.*?translator.*?\./i,
      /^Translation[:：]/i,
      /^翻译结果?[:：]/i,
      /^翻译[:：]/i,
      /^结果[:：]/i,
      /^直接翻译.*?[:：]/i,
      /^只输出翻译结果[:：]/i,
      /Only return the translated result.*?\./i,
      /不要添加任何解释.*?\./i,
      /^Here is the translation[:：]/i,
      /^The translation is[:：]/i,
      /^翻译为[:：]/i,
      /^答案[:：]/i,
      /^回答[:：]/i,
    ];

    let cleanedTranslation = translation.trim();

    // 应用清理模式
    for (const pattern of cleanPatterns) {
      cleanedTranslation = cleanedTranslation.replace(pattern, '').trim();
    }

    // 移除引号包围
    cleanedTranslation = cleanedTranslation.replace(/^["']|["']$/g, '').trim();

    // 如果清理后为空或太短，返回原文
    if (!cleanedTranslation || cleanedTranslation.length < 1) {
      return translation.trim();
    }

    return cleanedTranslation;
  }

  // 获取缓存统计
  getCacheStats() {
    return {
      size: this.translationCache.size,
      keys: Array.from(this.translationCache.keys())
    };
  }
}
