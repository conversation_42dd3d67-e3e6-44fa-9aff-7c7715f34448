import { Repository } from 'typeorm';
import { Coupon, DiscountType } from './entities/coupon.entity';
import { CreateCouponDto, UpdateCouponDto, CouponStatsDto } from './dto/coupon.dto';
export declare class CouponService {
    private couponRepository;
    constructor(couponRepository: Repository<Coupon>);
    findAll(): Promise<Coupon[]>;
    findOne(id: string): Promise<Coupon>;
    validateCoupon(code: string): Promise<{
        valid: boolean;
        coupon?: Coupon;
        message?: string;
    }>;
    useCoupon(code: string): Promise<Coupon>;
    create(createCouponDto: CreateCouponDto): Promise<Coupon>;
    createBatch(count: number, template: Omit<CreateCouponDto, 'code'>): Promise<Coupon[]>;
    generateRandomCoupons(count: number, discountType: DiscountType, discountValue: number, options?: {
        name?: string;
        description?: string;
        usageLimit?: number;
        minOrderAmount?: number;
        expiresAt?: Date;
    }): Promise<Coupon[]>;
    update(id: string, updateCouponDto: UpdateCouponDto): Promise<Coupon>;
    remove(id: string): Promise<void>;
    toggleStatus(id: string): Promise<Coupon>;
    getStats(): Promise<CouponStatsDto>;
    private generateCouponCode;
}
