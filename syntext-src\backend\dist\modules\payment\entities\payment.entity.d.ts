import { User } from '../../user/entities/user.entity';
import { Subscription } from '../../subscription/entities/subscription.entity';
export declare enum PaymentMethod {
    ALIPAY = "alipay",
    WECHAT = "wechat",
    STRIPE = "stripe"
}
export declare enum PaymentStatus {
    PENDING = "pending",
    PROCESSING = "processing",
    SUCCESS = "success",
    FAILED = "failed",
    CANCELLED = "cancelled",
    REFUNDED = "refunded"
}
export declare class Payment {
    id: string;
    userId: string;
    subscriptionId?: string;
    paymentMethod: PaymentMethod;
    status: PaymentStatus;
    orderNumber: string;
    thirdPartyOrderId?: string;
    amount: number;
    originalAmount: number;
    discountAmount: number;
    couponCode?: string;
    currency: string;
    description: string;
    callbackData?: string;
    failureReason?: string;
    paidAt?: Date;
    createdAt: Date;
    updatedAt: Date;
    user: User;
    subscription?: Subscription;
}
