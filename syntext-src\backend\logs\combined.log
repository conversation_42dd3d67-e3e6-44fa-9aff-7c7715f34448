{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T09:35:06.710Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T09:35:06.712Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T09:35:06.713Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T09:35:06.714Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T09:35:06.714Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T09:35:06.715Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T09:35:06.715Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T09:35:06.715Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T09:35:06.716Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T09:35:06.716Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T09:35:06.716Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T09:35:06.717Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T09:35:06.717Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T09:35:06.718Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T09:35:06.719Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T09:35:06.719Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T09:35:06.719Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T09:35:06.720Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T09:35:06.720Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T09:35:06.720Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T09:35:06.721Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T09:35:06.721Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T09:35:06.723Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T11:43:34.090Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T11:49:05.075Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T11:55:22.819Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T11:55:22.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T11:55:22.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T11:55:22.822Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T11:55:22.823Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T11:55:22.823Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T11:55:22.823Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T11:55:22.824Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T11:55:22.824Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T11:55:22.825Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T11:55:22.825Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T11:55:22.825Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T11:55:22.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T11:55:22.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T11:55:22.826Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T11:55:22.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T11:55:22.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T11:55:22.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T11:55:22.827Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T11:55:22.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T11:55:22.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T11:55:22.828Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T11:55:22.830Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T11:57:03.501Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 589384","timestamp":"2025-08-04T11:57:03.502Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T12:04:04.967Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 631628","timestamp":"2025-08-04T12:04:04.969Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T12:05:07.268Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 470810","timestamp":"2025-08-04T12:05:07.268Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T12:11:58.215Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 303976","timestamp":"2025-08-04T12:11:58.215Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T12:59:05.222Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T12:59:05.224Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T12:59:05.225Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T12:59:05.226Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T12:59:05.226Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T12:59:05.226Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T12:59:05.227Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T12:59:05.227Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T12:59:05.228Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T12:59:05.228Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T12:59:05.229Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T12:59:05.229Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T12:59:05.229Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T12:59:05.230Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T12:59:05.230Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T12:59:05.230Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T12:59:05.231Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T12:59:05.231Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T12:59:05.231Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T12:59:05.232Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T12:59:05.232Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T12:59:05.232Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T12:59:05.234Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T13:01:11.149Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 794392","timestamp":"2025-08-04T13:01:11.149Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translate\" message","timestamp":"2025-08-04T13:30:43.552Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"voice_stream\" message","timestamp":"2025-08-04T13:30:43.553Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"get_translation_history\" message","timestamp":"2025-08-04T13:30:43.553Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T13:30:43.554Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T13:30:43.556Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T13:30:43.556Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T13:30:43.557Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T13:30:43.557Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T13:30:43.558Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T13:30:43.559Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T13:30:43.559Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T13:30:43.560Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T13:30:43.560Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T13:30:43.560Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T13:30:43.561Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T13:30:43.561Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/translate, POST} route","timestamp":"2025-08-04T13:30:43.561Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/languages, GET} route","timestamp":"2025-08-04T13:30:43.561Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T13:30:43.562Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T13:30:43.562Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T13:30:43.562Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T13:30:43.562Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T13:30:43.562Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T13:30:43.563Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T13:30:43.563Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T13:30:43.563Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T13:30:43.564Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T13:30:43.568Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translate\" message","timestamp":"2025-08-04T13:32:42.485Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"voice_stream\" message","timestamp":"2025-08-04T13:32:42.485Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"get_translation_history\" message","timestamp":"2025-08-04T13:32:42.486Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T13:32:42.487Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T13:32:42.489Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T13:32:42.490Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T13:32:42.490Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T13:32:42.491Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T13:32:42.491Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T13:32:42.492Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T13:32:42.492Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T13:32:42.492Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T13:32:42.493Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T13:32:42.494Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T13:32:42.494Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T13:32:42.494Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/translate, POST} route","timestamp":"2025-08-04T13:32:42.495Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/languages, GET} route","timestamp":"2025-08-04T13:32:42.495Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T13:32:42.496Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T13:32:42.496Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T13:32:42.496Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T13:32:42.497Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T13:32:42.497Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T13:32:42.498Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T13:32:42.498Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T13:32:42.498Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T13:32:42.498Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T13:32:42.501Z"}
{"context":"TranslationService","level":"info","message":"Translating text from en-US to zh-CN","timestamp":"2025-08-04T13:33:21.938Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T13:37:13.283Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 473304","timestamp":"2025-08-04T13:37:13.286Z"}
{"context":"TranslationService","level":"info","message":"Translating text from en-US to zh-CN","timestamp":"2025-08-04T13:37:39.895Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T13:37:59.683Z"}
{"context":"TranslationService","level":"info","message":"Translating text from en-US to zh-CN","timestamp":"2025-08-04T13:38:18.245Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translate\" message","timestamp":"2025-08-04T14:10:29.625Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translate\" message","timestamp":"2025-08-04T14:11:12.546Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"voice_stream\" message","timestamp":"2025-08-04T14:11:12.547Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"get_translation_history\" message","timestamp":"2025-08-04T14:11:12.547Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T14:11:12.548Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T14:11:12.550Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T14:11:12.550Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T14:11:12.551Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T14:11:12.551Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T14:11:12.552Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T14:11:12.552Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T14:11:12.553Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T14:11:12.553Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T14:11:12.554Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T14:11:12.554Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T14:11:12.554Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T14:11:12.555Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/translate, POST} route","timestamp":"2025-08-04T14:11:12.555Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/languages, GET} route","timestamp":"2025-08-04T14:11:12.555Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T14:11:12.555Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T14:11:12.556Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T14:11:12.556Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T14:11:12.556Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T14:11:12.556Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T14:11:12.557Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T14:11:12.557Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T14:11:12.557Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T14:11:12.557Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T14:11:12.559Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translate\" message","timestamp":"2025-08-04T15:49:52.397Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"voice_stream\" message","timestamp":"2025-08-04T15:49:52.398Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"get_translation_history\" message","timestamp":"2025-08-04T15:49:52.398Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T15:49:52.401Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T15:49:52.403Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T15:49:52.404Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T15:49:52.405Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T15:49:52.405Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T15:49:52.406Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T15:49:52.406Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T15:49:52.407Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T15:49:52.408Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T15:49:52.409Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T15:49:52.409Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T15:49:52.410Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T15:49:52.410Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/translate, POST} route","timestamp":"2025-08-04T15:49:52.411Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/languages, GET} route","timestamp":"2025-08-04T15:49:52.412Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T15:49:52.412Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T15:49:52.412Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T15:49:52.413Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T15:49:52.413Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T15:49:52.413Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T15:49:52.413Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T15:49:52.413Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T15:49:52.414Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T15:49:52.414Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, POST} route","timestamp":"2025-08-04T15:49:52.414Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/batch, POST} route","timestamp":"2025-08-04T15:49:52.414Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/generate, POST} route","timestamp":"2025-08-04T15:49:52.415Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/stats, GET} route","timestamp":"2025-08-04T15:49:52.415Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, GET} route","timestamp":"2025-08-04T15:49:52.415Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, PUT} route","timestamp":"2025-08-04T15:49:52.416Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id/toggle, PUT} route","timestamp":"2025-08-04T15:49:52.416Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, DELETE} route","timestamp":"2025-08-04T15:49:52.416Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T15:49:52.419Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translate\" message","timestamp":"2025-08-04T15:50:05.112Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"voice_stream\" message","timestamp":"2025-08-04T15:50:05.113Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"get_translation_history\" message","timestamp":"2025-08-04T15:50:05.114Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T15:50:05.116Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T15:50:05.119Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T15:50:05.121Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T15:50:05.121Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T15:50:05.123Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T15:50:05.124Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T15:50:05.125Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T15:50:05.126Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T15:50:05.127Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T15:50:05.128Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T15:50:05.129Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T15:50:05.129Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T15:50:05.130Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/translate, POST} route","timestamp":"2025-08-04T15:50:05.130Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/languages, GET} route","timestamp":"2025-08-04T15:50:05.131Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T15:50:05.131Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T15:50:05.132Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T15:50:05.132Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T15:50:05.132Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T15:50:05.133Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T15:50:05.133Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T15:50:05.133Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T15:50:05.135Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T15:50:05.137Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, POST} route","timestamp":"2025-08-04T15:50:05.137Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/batch, POST} route","timestamp":"2025-08-04T15:50:05.138Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/generate, POST} route","timestamp":"2025-08-04T15:50:05.138Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/stats, GET} route","timestamp":"2025-08-04T15:50:05.139Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, GET} route","timestamp":"2025-08-04T15:50:05.139Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, PUT} route","timestamp":"2025-08-04T15:50:05.140Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id/toggle, PUT} route","timestamp":"2025-08-04T15:50:05.141Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, DELETE} route","timestamp":"2025-08-04T15:50:05.141Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T15:50:05.144Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T15:51:02.282Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 708031","timestamp":"2025-08-04T15:51:02.282Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T15:56:45.611Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 644922","timestamp":"2025-08-04T15:56:45.615Z"}
{"context":"TranslationService","level":"info","message":"Translating text from en-US to zh-CN","timestamp":"2025-08-04T15:57:26.006Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T16:01:18.613Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 605081","timestamp":"2025-08-04T16:01:18.613Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T16:05:44.974Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 711574","timestamp":"2025-08-04T16:05:44.976Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T16:06:20.896Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 469628","timestamp":"2025-08-04T16:06:20.896Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T16:07:51.983Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 114763","timestamp":"2025-08-04T16:07:51.984Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translate\" message","timestamp":"2025-08-04T16:15:02.233Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"voice_stream\" message","timestamp":"2025-08-04T16:15:02.234Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"get_translation_history\" message","timestamp":"2025-08-04T16:15:02.234Z"}
{"context":"RoutesResolver","level":"info","message":"AppController {/api/v1}:","timestamp":"2025-08-04T16:15:02.236Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1, GET} route","timestamp":"2025-08-04T16:15:02.238Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health, GET} route","timestamp":"2025-08-04T16:15:02.239Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T16:15:02.239Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T16:15:02.240Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T16:15:02.240Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T16:15:02.241Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T16:15:02.242Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T16:15:02.242Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T16:15:02.243Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T16:15:02.243Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T16:15:02.244Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T16:15:02.244Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T16:15:02.245Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T16:15:02.245Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T16:15:02.245Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/translate, POST} route","timestamp":"2025-08-04T16:15:02.246Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/languages, GET} route","timestamp":"2025-08-04T16:15:02.246Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T16:15:02.246Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T16:15:02.247Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T16:15:02.247Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T16:15:02.247Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T16:15:02.247Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T16:15:02.247Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T16:15:02.247Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T16:15:02.248Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T16:15:02.248Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, POST} route","timestamp":"2025-08-04T16:15:02.248Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/batch, POST} route","timestamp":"2025-08-04T16:15:02.248Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/generate, POST} route","timestamp":"2025-08-04T16:15:02.249Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/stats, GET} route","timestamp":"2025-08-04T16:15:02.249Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, GET} route","timestamp":"2025-08-04T16:15:02.249Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, PUT} route","timestamp":"2025-08-04T16:15:02.250Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id/toggle, PUT} route","timestamp":"2025-08-04T16:15:02.251Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, DELETE} route","timestamp":"2025-08-04T16:15:02.251Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T16:15:02.254Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translate\" message","timestamp":"2025-08-04T16:16:28.907Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"voice_stream\" message","timestamp":"2025-08-04T16:16:28.910Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"get_translation_history\" message","timestamp":"2025-08-04T16:16:28.911Z"}
{"context":"RoutesResolver","level":"info","message":"AppController {/api/v1}:","timestamp":"2025-08-04T16:16:28.912Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1, GET} route","timestamp":"2025-08-04T16:16:28.913Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health, GET} route","timestamp":"2025-08-04T16:16:28.914Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T16:16:28.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T16:16:28.915Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T16:16:28.915Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T16:16:28.915Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T16:16:28.916Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T16:16:28.916Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T16:16:28.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T16:16:28.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T16:16:28.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T16:16:28.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T16:16:28.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T16:16:28.920Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T16:16:28.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/translate, POST} route","timestamp":"2025-08-04T16:16:28.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/languages, GET} route","timestamp":"2025-08-04T16:16:28.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T16:16:28.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T16:16:28.922Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T16:16:28.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T16:16:28.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T16:16:28.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T16:16:28.923Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T16:16:28.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T16:16:28.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T16:16:28.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, POST} route","timestamp":"2025-08-04T16:16:28.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/batch, POST} route","timestamp":"2025-08-04T16:16:28.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/generate, POST} route","timestamp":"2025-08-04T16:16:28.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/stats, GET} route","timestamp":"2025-08-04T16:16:28.925Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, GET} route","timestamp":"2025-08-04T16:16:28.925Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, PUT} route","timestamp":"2025-08-04T16:16:28.926Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id/toggle, PUT} route","timestamp":"2025-08-04T16:16:28.926Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, DELETE} route","timestamp":"2025-08-04T16:16:28.926Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T16:16:28.929Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T16:16:56.561Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 511833","timestamp":"2025-08-04T16:16:56.562Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T16:18:24.958Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 410667","timestamp":"2025-08-04T16:18:24.958Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translate\" message","timestamp":"2025-08-04T16:19:46.217Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"voice_stream\" message","timestamp":"2025-08-04T16:19:46.218Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"get_translation_history\" message","timestamp":"2025-08-04T16:19:46.218Z"}
{"context":"RoutesResolver","level":"info","message":"AppController {/api/v1}:","timestamp":"2025-08-04T16:19:46.219Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1, GET} route","timestamp":"2025-08-04T16:19:46.221Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health, GET} route","timestamp":"2025-08-04T16:19:46.221Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T16:19:46.221Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth, GET} route","timestamp":"2025-08-04T16:19:46.222Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T16:19:46.222Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T16:19:46.223Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T16:19:46.224Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T16:19:46.224Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T16:19:46.225Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T16:19:46.225Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T16:19:46.226Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T16:19:46.227Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T16:19:46.227Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T16:19:46.228Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T16:19:46.229Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T16:19:46.229Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/translate, POST} route","timestamp":"2025-08-04T16:19:46.229Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/languages, GET} route","timestamp":"2025-08-04T16:19:46.229Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T16:19:46.230Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T16:19:46.230Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T16:19:46.230Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T16:19:46.230Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T16:19:46.230Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T16:19:46.231Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T16:19:46.231Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T16:19:46.231Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T16:19:46.231Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, POST} route","timestamp":"2025-08-04T16:19:46.232Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/batch, POST} route","timestamp":"2025-08-04T16:19:46.232Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/generate, POST} route","timestamp":"2025-08-04T16:19:46.232Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/stats, GET} route","timestamp":"2025-08-04T16:19:46.233Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, GET} route","timestamp":"2025-08-04T16:19:46.233Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, PUT} route","timestamp":"2025-08-04T16:19:46.234Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id/toggle, PUT} route","timestamp":"2025-08-04T16:19:46.235Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, DELETE} route","timestamp":"2025-08-04T16:19:46.235Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T16:19:46.237Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translate\" message","timestamp":"2025-08-04T16:26:35.976Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"voice_stream\" message","timestamp":"2025-08-04T16:26:35.976Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"get_translation_history\" message","timestamp":"2025-08-04T16:26:35.977Z"}
{"context":"RoutesResolver","level":"info","message":"AppController {/api/v1}:","timestamp":"2025-08-04T16:26:35.978Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1, GET} route","timestamp":"2025-08-04T16:26:35.979Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health, GET} route","timestamp":"2025-08-04T16:26:35.980Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T16:26:35.980Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth, GET} route","timestamp":"2025-08-04T16:26:35.981Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T16:26:35.981Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T16:26:35.982Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T16:26:35.982Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T16:26:35.983Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T16:26:35.983Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T16:26:35.984Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T16:26:35.984Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T16:26:35.984Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T16:26:35.985Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T16:26:35.985Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T16:26:35.986Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T16:26:35.986Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/translate, POST} route","timestamp":"2025-08-04T16:26:35.987Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/languages, GET} route","timestamp":"2025-08-04T16:26:35.987Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T16:26:35.987Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T16:26:35.987Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T16:26:35.987Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T16:26:35.988Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T16:26:35.988Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T16:26:35.988Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T16:26:35.988Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T16:26:35.988Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T16:26:35.988Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, POST} route","timestamp":"2025-08-04T16:26:35.989Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/batch, POST} route","timestamp":"2025-08-04T16:26:35.989Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/generate, POST} route","timestamp":"2025-08-04T16:26:35.989Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/stats, GET} route","timestamp":"2025-08-04T16:26:35.989Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, GET} route","timestamp":"2025-08-04T16:26:35.990Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, PUT} route","timestamp":"2025-08-04T16:26:35.990Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id/toggle, PUT} route","timestamp":"2025-08-04T16:26:35.990Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, DELETE} route","timestamp":"2025-08-04T16:26:35.991Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T16:26:35.993Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translate\" message","timestamp":"2025-08-04T16:31:02.795Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"voice_stream\" message","timestamp":"2025-08-04T16:31:02.796Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"get_translation_history\" message","timestamp":"2025-08-04T16:31:02.796Z"}
{"context":"RoutesResolver","level":"info","message":"AppController {/api/v1}:","timestamp":"2025-08-04T16:31:02.798Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1, GET} route","timestamp":"2025-08-04T16:31:02.800Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health, GET} route","timestamp":"2025-08-04T16:31:02.801Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T16:31:02.801Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth, GET} route","timestamp":"2025-08-04T16:31:02.801Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T16:31:02.802Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T16:31:02.803Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T16:31:02.804Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T16:31:02.804Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T16:31:02.804Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T16:31:02.805Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T16:31:02.806Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T16:31:02.806Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T16:31:02.807Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T16:31:02.807Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T16:31:02.808Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T16:31:02.808Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/translate, POST} route","timestamp":"2025-08-04T16:31:02.808Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/languages, GET} route","timestamp":"2025-08-04T16:31:02.809Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T16:31:02.809Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T16:31:02.809Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T16:31:02.809Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T16:31:02.810Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T16:31:02.810Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T16:31:02.810Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T16:31:02.810Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T16:31:02.811Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T16:31:02.811Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, POST} route","timestamp":"2025-08-04T16:31:02.811Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/batch, POST} route","timestamp":"2025-08-04T16:31:02.812Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/generate, POST} route","timestamp":"2025-08-04T16:31:02.813Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/stats, GET} route","timestamp":"2025-08-04T16:31:02.813Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, GET} route","timestamp":"2025-08-04T16:31:02.815Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, PUT} route","timestamp":"2025-08-04T16:31:02.815Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id/toggle, PUT} route","timestamp":"2025-08-04T16:31:02.816Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, DELETE} route","timestamp":"2025-08-04T16:31:02.816Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T16:31:02.817Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T16:31:52.247Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 614152","timestamp":"2025-08-04T16:31:52.247Z"}
{"context":"TranslationGateway","level":"info","message":"Client connected: <EMAIL> (utxX8saKd3IShYDJAAAC)","timestamp":"2025-08-04T16:32:06.946Z"}
{"context":"TranslationGateway","level":"info","message":"Translation <NAME_EMAIL>: 谁能解释一下薛定谔方程？","timestamp":"2025-08-04T16:32:11.956Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T16:32:11.956Z"}
{"context":"TranslationGateway","level":"info","message":"Translation <NAME_EMAIL>: 谁能解释一下薛定谔方程？","timestamp":"2025-08-04T16:32:14.958Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T16:32:14.958Z"}
{"context":"TranslationGateway","level":"info","message":"Translation <NAME_EMAIL>: 这个实验需要特别小心操作","timestamp":"2025-08-04T16:32:17.953Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T16:32:17.953Z"}
{"context":"TranslationGateway","level":"info","message":"Translation <NAME_EMAIL>: 今天我们来学习量子物理的基本概念","timestamp":"2025-08-04T16:32:20.965Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T16:32:20.965Z"}
{"context":"TranslationGateway","level":"info","message":"Translation <NAME_EMAIL>: 今天我们来学习量子物理的基本概念","timestamp":"2025-08-04T16:32:23.956Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T16:32:23.956Z"}
{"context":"TranslationGateway","level":"info","message":"Translation <NAME_EMAIL>: 今天我们来学习量子物理的基本概念","timestamp":"2025-08-04T16:32:26.951Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T16:32:26.952Z"}
{"context":"TranslationGateway","level":"info","message":"Client disconnected: <EMAIL> (utxX8saKd3IShYDJAAAC)","timestamp":"2025-08-04T16:32:34.216Z"}
{"context":"TranslationGateway","level":"info","message":"Client connected: <EMAIL> (-PFFpwyTBywFpBd-AAAF)","timestamp":"2025-08-04T16:33:20.741Z"}
{"context":"TranslationGateway","level":"info","message":"Client disconnected: <EMAIL> (-PFFpwyTBywFpBd-AAAF)","timestamp":"2025-08-04T16:33:21.931Z"}
{"context":"TranslationGateway","level":"info","message":"Client connected: <EMAIL> (AED5Ah5eBmqXL8u1AAAI)","timestamp":"2025-08-04T16:33:22.549Z"}
{"context":"TranslationGateway","level":"info","message":"Translation <NAME_EMAIL>: 谁能解释一下薛定谔方程？","timestamp":"2025-08-04T16:33:29.062Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T16:33:29.063Z"}
{"context":"TranslationGateway","level":"info","message":"Translation <NAME_EMAIL>: 请注意这个公式的推导过程","timestamp":"2025-08-04T16:33:32.064Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T16:33:32.064Z"}
{"context":"TranslationGateway","level":"info","message":"Translation <NAME_EMAIL>: 请注意这个公式的推导过程","timestamp":"2025-08-04T16:33:35.072Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T16:33:35.072Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T16:34:02.596Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 648438","timestamp":"2025-08-04T16:34:02.596Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T16:35:17.404Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 502724","timestamp":"2025-08-04T16:35:17.405Z"}
{"context":"TranslationGateway","level":"info","message":"Client disconnected: <EMAIL> (AED5Ah5eBmqXL8u1AAAI)","timestamp":"2025-08-04T16:35:35.610Z"}
{"context":"TranslationGateway","level":"info","message":"Client connected: <EMAIL> (K3SWFVVTYqO2rdViAAAK)","timestamp":"2025-08-04T16:35:35.645Z"}
{"context":"TranslationGateway","level":"info","message":"Client disconnected: <EMAIL> (K3SWFVVTYqO2rdViAAAK)","timestamp":"2025-08-04T16:35:51.533Z"}
{"context":"TranslationGateway","level":"info","message":"Client connected: <EMAIL> (BoNjHw0sthHT7d6WAAAM)","timestamp":"2025-08-04T16:35:51.548Z"}
{"context":"TranslationGateway","level":"info","message":"Client disconnected: <EMAIL> (BoNjHw0sthHT7d6WAAAM)","timestamp":"2025-08-04T16:36:21.740Z"}
{"context":"TranslationGateway","level":"info","message":"Client connected: <EMAIL> (vsn699x1uwiw6Ji7AAAP)","timestamp":"2025-08-04T16:40:42.043Z"}
{"context":"TranslationGateway","level":"info","message":"Client disconnected: <EMAIL> (vsn699x1uwiw6Ji7AAAP)","timestamp":"2025-08-04T16:40:42.885Z"}
{"context":"TranslationGateway","level":"info","message":"Client connected: <EMAIL> (1ir4dKaMZJL2C3aKAAAS)","timestamp":"2025-08-04T16:40:45.959Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translate\" message","timestamp":"2025-08-04T16:59:37.843Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"voice_stream\" message","timestamp":"2025-08-04T16:59:37.844Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"get_translation_history\" message","timestamp":"2025-08-04T16:59:37.844Z"}
{"context":"RoutesResolver","level":"info","message":"AppController {/api/v1}:","timestamp":"2025-08-04T16:59:37.846Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1, GET} route","timestamp":"2025-08-04T16:59:37.848Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health, GET} route","timestamp":"2025-08-04T16:59:37.849Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T16:59:37.849Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth, GET} route","timestamp":"2025-08-04T16:59:37.850Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T16:59:37.850Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T16:59:37.851Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T16:59:37.852Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T16:59:37.853Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T16:59:37.853Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T16:59:37.856Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T16:59:37.857Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T16:59:37.858Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T16:59:37.858Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T16:59:37.859Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T16:59:37.860Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T16:59:37.861Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/translate, POST} route","timestamp":"2025-08-04T16:59:37.862Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/languages, GET} route","timestamp":"2025-08-04T16:59:37.862Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T16:59:37.862Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T16:59:37.863Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T16:59:37.864Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T16:59:37.864Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T16:59:37.865Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T16:59:37.865Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T16:59:37.865Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T16:59:37.866Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T16:59:37.866Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, POST} route","timestamp":"2025-08-04T16:59:37.866Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/batch, POST} route","timestamp":"2025-08-04T16:59:37.868Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/generate, POST} route","timestamp":"2025-08-04T16:59:37.869Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/stats, GET} route","timestamp":"2025-08-04T16:59:37.870Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, GET} route","timestamp":"2025-08-04T16:59:37.871Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, PUT} route","timestamp":"2025-08-04T16:59:37.872Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id/toggle, PUT} route","timestamp":"2025-08-04T16:59:37.873Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/:id, DELETE} route","timestamp":"2025-08-04T16:59:37.873Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T16:59:37.878Z"}
{"context":"TranslationGateway","level":"info","message":"Client connected: <EMAIL> (e3y_Av6TjcAd10fXAAAB) from ::ffff:172.22.0.1","timestamp":"2025-08-04T16:59:40.756Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T17:00:25.931Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 966979","timestamp":"2025-08-04T17:00:25.931Z"}
{"context":"TranslationGateway","level":"info","message":"Client connected: <EMAIL> (l1Zbyi45ytKPjP3KAAAE) from ::ffff:172.22.0.1","timestamp":"2025-08-04T17:00:39.036Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326847067_pvcre2an6","timestamp":"2025-08-04T17:00:47.070Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4992 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:47.071Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326847365_ratcph0ot","timestamp":"2025-08-04T17:00:47.369Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:47.370Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326847665_3uvha71j9","timestamp":"2025-08-04T17:00:47.667Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:47.668Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326848027_4x554q92m","timestamp":"2025-08-04T17:00:48.030Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:48.030Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326848386_puzgk3tr6","timestamp":"2025-08-04T17:00:48.388Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:48.388Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326848686_fpjbuuytu","timestamp":"2025-08-04T17:00:48.688Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:48.688Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326848988_d0qmo9ck3","timestamp":"2025-08-04T17:00:48.990Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:48.991Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326849344_5bjrerxvj","timestamp":"2025-08-04T17:00:49.346Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:49.346Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326849646_xq5pvz0ik","timestamp":"2025-08-04T17:00:49.647Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:49.648Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326849947_1gqx94ec6","timestamp":"2025-08-04T17:00:49.948Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:49.949Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326850305_r79tgjjgu","timestamp":"2025-08-04T17:00:50.306Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:50.306Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326850665_3l05c04ip","timestamp":"2025-08-04T17:00:50.667Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:50.667Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326850966_jqygoxhqv","timestamp":"2025-08-04T17:00:50.969Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:50.969Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326851325_b0lrp6rfb","timestamp":"2025-08-04T17:00:51.328Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:51.328Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326851625_5pzt3akcw","timestamp":"2025-08-04T17:00:51.627Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:51.627Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326851925_7i9xj9kxz","timestamp":"2025-08-04T17:00:51.928Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:51.928Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326852285_pr2ov52v3","timestamp":"2025-08-04T17:00:52.286Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:52.286Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326852587_031srdjn4","timestamp":"2025-08-04T17:00:52.588Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:52.591Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326852946_yoic15xyt","timestamp":"2025-08-04T17:00:52.948Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:52.948Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体条款，能否请您详细说明一下？另外，如果出现质量问题，维修流程大概需要多长时间呢？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:53.266Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体条款，能否请您详细说明一下？另外，如果出现质量问题，维修流程大概需要多长时间呢？\"\" (confidence: 0.8999999999999999, duration: 5598ms)","timestamp":"2025-08-04T17:00:53.266Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:53.266Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体条款，能否请您详细说明一下？另外，如果出现质量问题，维修流程大概需要多长时间呢？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:53.267Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326853304_6svrwnbdw","timestamp":"2025-08-04T17:00:53.306Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:53.306Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326853606_m35vdty5l","timestamp":"2025-08-04T17:00:53.608Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:53.609Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果需要付费维修的话，大概的费用范围是多少呢？\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:53.657Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果需要付费维修的话，大概的费用范围是多少呢？\" (confidence: 0.8999999999999999, duration: 5627ms)","timestamp":"2025-08-04T17:00:53.657Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:53.658Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果需要付费维修的话，大概的费用范围是多少呢？\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:53.658Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326853965_as7o8lahg","timestamp":"2025-08-04T17:00:53.967Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:53.968Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内，大概需要多少维修费用呢？\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:53.983Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内，大概需要多少维修费用呢？\" (confidence: 0.8999999999999999, duration: 6913ms)","timestamp":"2025-08-04T17:00:53.983Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:53.984Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内，大概需要多少维修费用呢？\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:53.984Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"您好，我想咨询一下关于产品保修的具体政策，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:54.056Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"您好，我想咨询一下关于产品保修的具体政策，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？\" (confidence: 0.8999999999999999, duration: 5065ms)","timestamp":"2025-08-04T17:00:54.056Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:54.056Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"您好，我想咨询一下关于产品保修的具体政策，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:54.056Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和需要准备的证明材料？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:54.142Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和需要准备的证明材料？\"\" (confidence: 0.8999999999999999, duration: 6774ms)","timestamp":"2025-08-04T17:00:54.143Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:54.143Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和需要准备的证明材料？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:54.143Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和服务流程？另外，如果需要送修的话，大概需要多长时间能处理好？\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:54.156Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和服务流程？另外，如果需要送修的话，大概需要多长时间能处理好？\" (confidence: 0.8999999999999999, duration: 5768ms)","timestamp":"2025-08-04T17:00:54.156Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:54.156Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和服务流程？另外，如果需要送修的话，大概需要多长时间能处理好？\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:54.157Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326854326_mwxjwzzoy","timestamp":"2025-08-04T17:00:54.328Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:54.328Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"您好，我想咨询一下关于产品保修的具体政策，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:54.627Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"您好，我想咨询一下关于产品保修的具体政策，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？\" (confidence: 0.8999999999999999, duration: 4979ms)","timestamp":"2025-08-04T17:00:54.627Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:54.628Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"您好，我想咨询一下关于产品保修的具体政策，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:54.628Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326854686_clrg0rb4x","timestamp":"2025-08-04T17:00:54.688Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:54.688Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，想了解是否在保修范围内以及需要准备哪些材料。另外，如果不在保修期的话，维修费用大概是多少？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:54.822Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，想了解是否在保修范围内以及需要准备哪些材料。另外，如果不在保修期的话，维修费用大概是多少？\"\" (confidence: 0.8999999999999999, duration: 6134ms)","timestamp":"2025-08-04T17:00:54.823Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:54.823Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，想了解是否在保修范围内以及需要准备哪些材料。另外，如果不在保修期的话，维修费用大概是多少？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:54.823Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326854988_tx02bkq5t","timestamp":"2025-08-04T17:00:54.994Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:54.995Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326855347_8pby574yu","timestamp":"2025-08-04T17:00:55.349Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:55.349Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:55.513Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？\"\" (confidence: 0.8999999999999999, duration: 4544ms)","timestamp":"2025-08-04T17:00:55.514Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:55.514Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:55.514Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内的话，大概需要多少维修费用？\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:55.519Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内的话，大概需要多少维修费用？\" (confidence: 0.8999999999999999, duration: 6173ms)","timestamp":"2025-08-04T17:00:55.519Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:55.520Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内的话，大概需要多少维修费用？\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:55.520Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326855647_b1xwm3nu2","timestamp":"2025-08-04T17:00:55.649Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:55.650Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326856005_qagff3rds","timestamp":"2025-08-04T17:00:56.007Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:56.008Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"请问您能帮我查一下明天的天气情况吗？我打算出门但不确定要不要带伞。\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:56.151Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"请问您能帮我查一下明天的天气情况吗？我打算出门但不确定要不要带伞。\" (confidence: 0.8999999999999999, duration: 4524ms)","timestamp":"2025-08-04T17:00:56.151Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:56.151Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"请问您能帮我查一下明天的天气情况吗？我打算出门但不确定要不要带伞。\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:56.151Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326856305_q5d5vqh0f","timestamp":"2025-08-04T17:00:56.305Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:56.305Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要准备的材料？谢谢。\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:56.307Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要准备的材料？谢谢。\" (confidence: 0.8999999999999999, duration: 6001ms)","timestamp":"2025-08-04T17:00:56.307Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:56.307Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要准备的材料？谢谢。\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:56.307Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326856605_zvqwdp8qn","timestamp":"2025-08-04T17:00:56.605Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:56.606Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"您好，我想咨询一下最近新推出的服务套餐，能麻烦您详细介绍一下具体的资费标准和包含的内容吗？我对比了几家运营商，感觉你们这个套餐的流量部分比较有优势。\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:56.614Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"您好，我想咨询一下最近新推出的服务套餐，能麻烦您详细介绍一下具体的资费标准和包含的内容吗？我对比了几家运营商，感觉你们这个套餐的流量部分比较有优势。\" (confidence: 0.8999999999999999, duration: 6665ms)","timestamp":"2025-08-04T17:00:56.614Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:56.614Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"您好，我想咨询一下最近新推出的服务套餐，能麻烦您详细介绍一下具体的资费标准和包含的内容吗？我对比了几家运营商，感觉你们这个套餐的流量部分比较有优势。\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:56.614Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:56.727Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\" (confidence: 0.8999999999999999, duration: 6060ms)","timestamp":"2025-08-04T17:00:56.727Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:56.728Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:56.729Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内的话，大概需要多少维修费用呢？\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:56.904Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内的话，大概需要多少维修费用呢？\" (confidence: 0.8999999999999999, duration: 5576ms)","timestamp":"2025-08-04T17:00:56.904Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:56.904Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内的话，大概需要多少维修费用呢？\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:56.904Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326856965_ypdary4ak","timestamp":"2025-08-04T17:00:56.966Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:56.967Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326857325_cqgg05tmx","timestamp":"2025-08-04T17:00:57.326Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:57.326Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:57.570Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\"\" (confidence: 0.8999999999999999, duration: 5642ms)","timestamp":"2025-08-04T17:00:57.571Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:57.571Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:57.571Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326857687_spxi78u0h","timestamp":"2025-08-04T17:00:57.688Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:57.688Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326858047_bxwfjt1my","timestamp":"2025-08-04T17:00:58.049Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:58.050Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果需要付费的话，大概的费用范围是多少？谢谢。\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:58.081Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果需要付费的话，大概的费用范围是多少？谢谢。\"\" (confidence: 0.8999999999999999, duration: 5795ms)","timestamp":"2025-08-04T17:00:58.081Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:58.082Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果需要付费的话，大概的费用范围是多少？谢谢。\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:58.082Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326858407_0cta90hwl","timestamp":"2025-08-04T17:00:58.408Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:58.408Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:58.668Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\"\" (confidence: 0.8999999999999999, duration: 6077ms)","timestamp":"2025-08-04T17:00:58.668Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:58.669Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:58.669Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内，大概需要多少维修费用？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:58.677Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内，大概需要多少维修费用？\"\" (confidence: 0.8999999999999999, duration: 5372ms)","timestamp":"2025-08-04T17:00:58.678Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:58.679Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内，大概需要多少维修费用？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:58.679Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"您好，我想咨询一下关于产品保修的具体条款，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外能否告知最近的售后服务点在哪里？\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:58.750Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"您好，我想咨询一下关于产品保修的具体条款，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外能否告知最近的售后服务点在哪里？\" (confidence: 0.8999999999999999, duration: 5802ms)","timestamp":"2025-08-04T17:00:58.750Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:58.750Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"您好，我想咨询一下关于产品保修的具体条款，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外能否告知最近的售后服务点在哪里？\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:58.750Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326858765_8g2ir19sl","timestamp":"2025-08-04T17:00:58.765Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:58.766Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326859126_pvu5ljuwq","timestamp":"2025-08-04T17:00:59.126Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 5796 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:59.127Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326859425_uu1zh1qdm","timestamp":"2025-08-04T17:00:59.426Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:59.426Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I would like to inquire about the specific terms of the product warranty. Could you please provide a detailed explanation? Additionally, in case of quality issues, how long does the repair process typically take?\"\"","timestamp":"2025-08-04T17:00:59.459Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体条款，能否请您详细说明一下？另外，如果出现质量问题，维修流程大概需要多长时间呢？\"\"","timestamp":"2025-08-04T17:00:59.459Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"Hello, I would like to inquire about the specific warranty policy for the product. My device has recently encountered some issues, and I'm not sure if it qualifies for free repair. Additionally, if paid repair is required, what would be the approximate cost range?\"","timestamp":"2025-08-04T17:00:59.461Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果需要付费维修的话，大概的费用范围是多少呢？\"","timestamp":"2025-08-04T17:00:59.462Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"Hello, I would like to inquire about the specific warranty policy for the product. My device has recently encountered some issues, and I'm not sure if it qualifies for free repair.\"","timestamp":"2025-08-04T17:00:59.724Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"您好，我想咨询一下关于产品保修的具体政策，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？\"","timestamp":"2025-08-04T17:00:59.724Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326859726_e7crnzx8u","timestamp":"2025-08-04T17:00:59.726Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 4830 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:59.726Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"Hello, I would like to inquire about the specific warranty policy for the product. My device has recently encountered some issues, and I'm not sure if it qualifies for free repair.\"","timestamp":"2025-08-04T17:00:59.921Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"您好，我想咨询一下关于产品保修的具体政策，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？\"","timestamp":"2025-08-04T17:00:59.921Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合保修范围？另外，如果需要维修的话，大概需要多长时间能处理好呢？\" (confidence: 0.900)","timestamp":"2025-08-04T17:00:59.925Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合保修范围？另外，如果需要维修的话，大概需要多长时间能处理好呢？\" (confidence: 0.8999999999999999, duration: 6317ms)","timestamp":"2025-08-04T17:00:59.925Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:00:59.925Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合保修范围？另外，如果需要维修的话，大概需要多长时间能处理好呢？\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:00:59.925Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326859972_ym1dtqxte","timestamp":"2025-08-04T17:00:59.973Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 3863 bytes, language: zh-CN","timestamp":"2025-08-04T17:00:59.973Z"}
{"context":"SpeechService","level":"error","message":"DeepSeek Speech API error:","stack":[{"code":"ERR_OUT_OF_RANGE"}],"timestamp":"2025-08-04T17:00:59.975Z"}
{"context":"SpeechService","level":"error","message":"Speech to text failed:","stack":[{"code":"ERR_OUT_OF_RANGE"}],"timestamp":"2025-08-04T17:00:59.975Z"}
{"context":"TranslationGateway","level":"info","message":"Processing voice <NAME_EMAIL>, language: zh-CN, sessionId: session_1754326859982_d0vlqfsoq","timestamp":"2025-08-04T17:01:00.036Z"}
{"context":"SpeechService","level":"info","message":"Processing audio with DeepSeek: 212681 bytes, language: zh-CN","timestamp":"2025-08-04T17:01:00.036Z"}
{"context":"SpeechService","level":"error","message":"DeepSeek Speech API error:","stack":[{"code":"ERR_OUT_OF_RANGE"}],"timestamp":"2025-08-04T17:01:00.040Z"}
{"context":"SpeechService","level":"error","message":"Speech to text failed:","stack":[{"code":"ERR_OUT_OF_RANGE"}],"timestamp":"2025-08-04T17:01:00.040Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:00.150Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\" (confidence: 0.8999999999999999, duration: 5156ms)","timestamp":"2025-08-04T17:01:00.150Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:00.150Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:00.150Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内的话，大概需要多少维修费用？\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:00.308Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内的话，大概需要多少维修费用？\" (confidence: 0.8999999999999999, duration: 5620ms)","timestamp":"2025-08-04T17:01:00.308Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:00.309Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内的话，大概需要多少维修费用？\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:00.309Z"}
{"context":"SpeechService","level":"info","message":"Enhanced mock speech recognition: \"系统运行正常\" (confidence: 0.935, category: technical, isQuestion: false)","timestamp":"2025-08-04T17:01:00.574Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"系统运行正常\" (confidence: 0.9349397905357786, duration: 601ms)","timestamp":"2025-08-04T17:01:00.574Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:00.575Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"系统运行正常\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:00.575Z"}
{"context":"SpeechService","level":"info","message":"Enhanced mock speech recognition: \"这个功能怎么使用？\" (confidence: 0.924, category: question, isQuestion: true)","timestamp":"2025-08-04T17:01:00.698Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"这个功能怎么使用？\" (confidence: 0.9242845369240132, duration: 662ms)","timestamp":"2025-08-04T17:01:00.698Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:00.698Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"这个功能怎么使用？\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:00.699Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I would like to inquire about the specific warranty policy for the product. My device has recently encountered some issues, and I'm not sure if it qualifies for free repair?\"\"","timestamp":"2025-08-04T17:01:00.708Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？\"\"","timestamp":"2025-08-04T17:01:00.708Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:00.768Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\"\" (confidence: 0.8999999999999999, duration: 5420ms)","timestamp":"2025-08-04T17:01:00.769Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:00.769Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:00.769Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:00.910Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好？\"\" (confidence: 0.8999999999999999, duration: 6582ms)","timestamp":"2025-08-04T17:01:00.910Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:00.911Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:00.911Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"Hello, I would like to inquire about the specific warranty policy for your products. My device has recently encountered some minor issues, and I'm wondering if it qualifies for free repair. Additionally, if it's not covered under warranty, approximately how much would the repair cost be?\"","timestamp":"2025-08-04T17:01:01.101Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内，大概需要多少维修费用呢？\"","timestamp":"2025-08-04T17:01:01.101Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:01.131Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\" (confidence: 0.8999999999999999, duration: 5481ms)","timestamp":"2025-08-04T17:01:01.131Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:01.131Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:01.131Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"Could you please check the weather forecast for tomorrow? I'm planning to go out but not sure if I should bring an umbrella.\"","timestamp":"2025-08-04T17:01:01.132Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"请问您能帮我查一下明天的天气情况吗？我打算出门但不确定要不要带伞。\"","timestamp":"2025-08-04T17:01:01.132Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合保修范围。能否请您详细说明一下哪些情况可以免费维修？另外，如果需要寄送维修的话，大概需要多长时间能处理好？谢谢。\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:01.300Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合保修范围。能否请您详细说明一下哪些情况可以免费维修？另外，如果需要寄送维修的话，大概需要多长时间能处理好？谢谢。\"\" (confidence: 0.8999999999999999, duration: 7332ms)","timestamp":"2025-08-04T17:01:01.300Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:01.301Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合保修范围。能否请您详细说明一下哪些情况可以免费维修？另外，如果需要寄送维修的话，大概需要多长时间能处理好？谢谢。\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:01.301Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I would like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'd like to know if it's covered under warranty and what materials I need to prepare. Additionally, if it's out of warranty, approximately how much would the repair cost be?\"\"","timestamp":"2025-08-04T17:01:01.330Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，想了解是否在保修范围内以及需要准备哪些材料。另外，如果不在保修期的话，维修费用大概是多少？\"\"","timestamp":"2025-08-04T17:01:01.330Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm wondering if it qualifies for free repairs. Could you please provide detailed information on the warranty coverage and the required supporting documents?\"\"","timestamp":"2025-08-04T17:01:01.394Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和需要准备的证明材料？\"\"","timestamp":"2025-08-04T17:01:01.395Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果需要付费维修的话，大概的费用范围是多少呢？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:01.728Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果需要付费维修的话，大概的费用范围是多少呢？\"\" (confidence: 0.8999999999999999, duration: 5423ms)","timestamp":"2025-08-04T17:01:01.728Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:01.730Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果需要付费维修的话，大概的费用范围是多少呢？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:01.731Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合保修条件？另外，如果需要维修的话，大概需要多长时间能处理好？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:01.739Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合保修条件？另外，如果需要维修的话，大概需要多长时间能处理好？\"\" (confidence: 0.8999999999999999, duration: 5733ms)","timestamp":"2025-08-04T17:01:01.740Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:01.740Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合保修条件？另外，如果需要维修的话，大概需要多长时间能处理好？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:01.741Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"Hello, I would like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm wondering if it qualifies for free repair? Additionally, if it's not covered by the warranty, approximately how much would the repair cost be?\"","timestamp":"2025-08-04T17:01:01.829Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内的话，大概需要多少维修费用？\"","timestamp":"2025-08-04T17:01:01.829Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"Hello, I'd like to inquire about the specific terms of the product warranty. My device has recently encountered some issues, and I'm not entirely sure if it qualifies for free repair. Could you please provide a detailed explanation of the warranty coverage and service process? Additionally, if it needs to be sent in for repair, approximately how long would the process take?\"","timestamp":"2025-08-04T17:01:01.989Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和服务流程？另外，如果需要送修的话，大概需要多长时间能处理好？\"","timestamp":"2025-08-04T17:01:01.989Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要准备的材料？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:02.395Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要准备的材料？\"\" (confidence: 0.8999999999999999, duration: 5428ms)","timestamp":"2025-08-04T17:01:02.395Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:02.395Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要准备的材料？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:02.395Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"Hello, I would like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm not entirely sure if it qualifies for free repairs. Could you please provide detailed information on the warranty coverage and the materials I need to prepare? Thank you.\"","timestamp":"2025-08-04T17:01:02.480Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要准备的材料？谢谢。\"","timestamp":"2025-08-04T17:01:02.480Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"Hello, I'd like to inquire about the newly launched service package. Could you please provide a detailed introduction to the specific pricing standards and included features? I've compared several carriers and feel that the data allowance in your package seems particularly advantageous.\"","timestamp":"2025-08-04T17:01:02.599Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"您好，我想咨询一下最近新推出的服务套餐，能麻烦您详细介绍一下具体的资费标准和包含的内容吗？我对比了几家运营商，感觉你们这个套餐的流量部分比较有优势。\"","timestamp":"2025-08-04T17:01:02.599Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"Hello, I would like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm wondering if it qualifies for free repair? Additionally, if it's not covered by the warranty, approximately how much would the repair cost be?\"","timestamp":"2025-08-04T17:01:02.678Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内的话，大概需要多少维修费用呢？\"","timestamp":"2025-08-04T17:01:02.678Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm not sure if it qualifies for free repairs. Could you please provide detailed information on the warranty coverage and the required supporting documents?\"\"","timestamp":"2025-08-04T17:01:02.699Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\"","timestamp":"2025-08-04T17:01:02.699Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:02.829Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好？\"\" (confidence: 0.8999999999999999, duration: 5503ms)","timestamp":"2025-08-04T17:01:02.829Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:02.830Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:02.830Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合免费维修的条件？另外，如果需要付费的话，大概的费用范围是多少呢？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:02.953Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合免费维修的条件？另外，如果需要付费的话，大概的费用范围是多少呢？\"\" (confidence: 0.8999999999999999, duration: 5265ms)","timestamp":"2025-08-04T17:01:02.953Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:02.953Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合免费维修的条件？另外，如果需要付费的话，大概的费用范围是多少呢？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:02.953Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合免费维修的条件？另外，如果需要付费的话，大概的费用范围是多少呢？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:02.987Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合免费维修的条件？另外，如果需要付费的话，大概的费用范围是多少呢？\"\" (confidence: 0.8999999999999999, duration: 6382ms)","timestamp":"2025-08-04T17:01:02.988Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:02.988Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合免费维修的条件？另外，如果需要付费的话，大概的费用范围是多少呢？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:02.988Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合保修范围。另外，如果需要维修的话，大概需要多长时间能处理好？谢谢。\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:03.571Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合保修范围。另外，如果需要维修的话，大概需要多长时间能处理好？谢谢。\"\" (confidence: 0.8999999999999999, duration: 5522ms)","timestamp":"2025-08-04T17:01:03.571Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:03.572Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合保修范围。另外，如果需要维修的话，大概需要多长时间能处理好？谢谢。\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:03.572Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:04.059Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\" (confidence: 0.8999999999999999, duration: 5651ms)","timestamp":"2025-08-04T17:01:04.059Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:04.059Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:04.059Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:04.136Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\" (confidence: 0.8999999999999999, duration: 5370ms)","timestamp":"2025-08-04T17:01:04.136Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:04.136Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:04.136Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"How do I use this feature?\"","timestamp":"2025-08-04T17:01:04.359Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"这个功能怎么使用？\"","timestamp":"2025-08-04T17:01:04.359Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I would like to inquire about the specific terms of the product warranty. My device has recently encountered some issues, and I'm not entirely sure if it qualifies for free repair. Could you please provide a detailed explanation of the warranty coverage and the required supporting documents?\"\"","timestamp":"2025-08-04T17:01:04.386Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\"\"","timestamp":"2025-08-04T17:01:04.386Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I would like to inquire about the specific terms of the product warranty. My device has recently encountered some issues, and I'm not sure if it qualifies for free repair. Additionally, if there is a charge, what would be the approximate cost range? Thank you.\"\"","timestamp":"2025-08-04T17:01:04.648Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果需要付费的话，大概的费用范围是多少？谢谢。\"\"","timestamp":"2025-08-04T17:01:04.648Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"The system is operating normally.\"","timestamp":"2025-08-04T17:01:04.762Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I would like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm wondering if it qualifies for free repairs? Additionally, if it's not covered by the warranty, approximately how much would the repair cost?\"\"","timestamp":"2025-08-04T17:01:04.843Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内，大概需要多少维修费用？\"\"","timestamp":"2025-08-04T17:01:04.843Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好呢？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:04.864Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好呢？\"\" (confidence: 0.8999999999999999, duration: 5438ms)","timestamp":"2025-08-04T17:01:04.864Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:04.864Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好呢？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:04.864Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，请问这个产品的保修期是多久？我昨天刚购买的，但发现说明书上写的不是很清楚。\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:04.955Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，请问这个产品的保修期是多久？我昨天刚购买的，但发现说明书上写的不是很清楚。\"\" (confidence: 0.8999999999999999, duration: 5229ms)","timestamp":"2025-08-04T17:01:04.955Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:04.955Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，请问这个产品的保修期是多久？我昨天刚购买的，但发现说明书上写的不是很清楚。\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:04.955Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm not entirely sure if it qualifies for free repairs. Could you please provide detailed information on the warranty coverage and the required supporting documents?\"\"","timestamp":"2025-08-04T17:01:04.973Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\"\"","timestamp":"2025-08-04T17:01:04.973Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"Hello, I would like to inquire about the specific terms of the product warranty. My device has recently encountered some issues, and I'm not sure if it qualifies for free repair. Also, could you let me know where the nearest after-sales service center is located?\"","timestamp":"2025-08-04T17:01:05.050Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"您好，我想咨询一下关于产品保修的具体条款，我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外能否告知最近的售后服务点在哪里？\"","timestamp":"2025-08-04T17:01:05.051Z"}
{"context":"SpeechService","level":"info","message":"DeepSeek Speech result: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些小问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好？\"\" (confidence: 0.900)","timestamp":"2025-08-04T17:01:05.364Z"}
{"context":"TranslationGateway","level":"info","message":"Speech recognition result: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些小问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好？\"\" (confidence: 0.8999999999999999, duration: 6237ms)","timestamp":"2025-08-04T17:01:05.364Z"}
{"context":"TranslationService","level":"info","message":"Translating text from zh-CN to en-US","timestamp":"2025-08-04T17:01:05.365Z"}
{"context":"TranslationService","level":"info","message":"Translating with DeepSeek: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些小问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好？\"\" (zh-CN -> en-US)","timestamp":"2025-08-04T17:01:05.365Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"Hello, I would like to inquire about the specific warranty policy for your products. My device has recently encountered some minor issues, and I'm wondering if they fall under the warranty coverage. Additionally, if repairs are needed, approximately how long would the process take?\"","timestamp":"2025-08-04T17:01:06.679Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合保修范围？另外，如果需要维修的话，大概需要多长时间能处理好呢？\"","timestamp":"2025-08-04T17:01:06.680Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm not sure if it qualifies for free repairs. Could you please provide detailed information on the warranty coverage and the required supporting documents?\"\"","timestamp":"2025-08-04T17:01:06.875Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\"","timestamp":"2025-08-04T17:01:06.875Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"Hello, I would like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm wondering if it qualifies for free repairs? Additionally, if it's not covered by the warranty, approximately how much would the repair cost be?\"","timestamp":"2025-08-04T17:01:07.123Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果不在保修范围内的话，大概需要多少维修费用？\"","timestamp":"2025-08-04T17:01:07.124Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific terms of the product warranty. My device has recently encountered some issues, and I'm not sure if it qualifies for free repairs. Could you please provide detailed information on the warranty coverage and the required supporting documents?\"\"","timestamp":"2025-08-04T17:01:07.225Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\"","timestamp":"2025-08-04T17:01:07.225Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm not entirely sure if it qualifies for free repairs. Could you please provide detailed information on the warranty coverage and the required supporting documents?\"\"","timestamp":"2025-08-04T17:01:07.228Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\"\"","timestamp":"2025-08-04T17:01:07.228Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I would like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm not sure if they fall under the warranty coverage. Additionally, if repairs are needed, approximately how long would it take to resolve them?\"\"","timestamp":"2025-08-04T17:01:07.409Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好？\"\"","timestamp":"2025-08-04T17:01:07.410Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326859972_ym1dtqxte in 8318ms","timestamp":"2025-08-04T17:01:08.291Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm wondering if it qualifies for warranty coverage. Additionally, if repairs are needed, approximately how long would the process take?\"\"","timestamp":"2025-08-04T17:01:08.821Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合保修条件？另外，如果需要维修的话，大概需要多长时间能处理好？\"\"","timestamp":"2025-08-04T17:01:08.822Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm wondering if it qualifies for free repairs. Additionally, if paid repairs are required, what would be the approximate cost range?\"\"","timestamp":"2025-08-04T17:01:08.936Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？另外，如果需要付费维修的话，大概的费用范围是多少呢？\"\"","timestamp":"2025-08-04T17:01:08.937Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm not entirely sure if they fall under the warranty coverage. Could you please provide detailed information on which situations qualify for free repairs? Additionally, if the device needs to be sent in for servicing, approximately how long would the repair process take? Thank you.\"\"","timestamp":"2025-08-04T17:01:08.945Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合保修范围。能否请您详细说明一下哪些情况可以免费维修？另外，如果需要寄送维修的话，大概需要多长时间能处理好？谢谢。\"\"","timestamp":"2025-08-04T17:01:08.946Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific terms of the product warranty. My device has recently encountered some issues, and I'm not entirely sure if it qualifies for free repair. Could you please provide detailed information on the warranty coverage and the materials I need to prepare?\"\"","timestamp":"2025-08-04T17:01:09.199Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要准备的材料？\"\"","timestamp":"2025-08-04T17:01:09.200Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"很抱歉，我无法直接获取实时天气数据。建议您可以通过以下方式查询：\n1. 查看手机上的天气应用\n2. 搜索\"XX城市明天天气\"(请替换XX为您的城市名)\n3. 拨打当地气象服务电话\n\n一般建议随身携带折...\"","timestamp":"2025-08-04T17:01:09.253Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326851625_5pzt3akcw in 17634ms","timestamp":"2025-08-04T17:01:09.261Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, may I ask how long the warranty period is for this product? I just purchased it yesterday, but the information in the manual isn't very clear.\"\"","timestamp":"2025-08-04T17:01:09.982Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，请问这个产品的保修期是多久？我昨天刚购买的，但发现说明书上写的不是很清楚。\"\"","timestamp":"2025-08-04T17:01:09.982Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm not sure if it qualifies for free repair. Could you please provide detailed information on the warranty coverage and the required supporting documents?\"\"","timestamp":"2025-08-04T17:01:09.985Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否符合免费维修的条件？能否请您详细说明一下保修范围和所需提供的证明材料？\"\"","timestamp":"2025-08-04T17:01:09.985Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific warranty policy for your products. My device has recently encountered some minor issues, and I'm wondering if it qualifies for free repairs? Additionally, if there are any charges involved, could you give me an approximate cost range?\"\"","timestamp":"2025-08-04T17:01:10.096Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合免费维修的条件？另外，如果需要付费的话，大概的费用范围是多少呢？\"\"","timestamp":"2025-08-04T17:01:10.096Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific warranty policy for your products. My device has recently encountered some minor issues, and I'm wondering if it qualifies for free repairs? Additionally, if there are any charges involved, what would be the approximate cost range?\"\"","timestamp":"2025-08-04T17:01:10.255Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些小问题，不知道是否符合免费维修的条件？另外，如果需要付费的话，大概的费用范围是多少呢？\"\"","timestamp":"2025-08-04T17:01:10.255Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific warranty policy for your products. My device has been experiencing some issues recently, and I'm not sure if they're covered under warranty. Also, if repairs are needed, approximately how long would it take to resolve the issue?\"\"","timestamp":"2025-08-04T17:01:10.410Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好？\"\"","timestamp":"2025-08-04T17:01:10.410Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"Hello, I would like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm not entirely sure if it qualifies for free repairs. Could you please provide detailed information on the warranty coverage and the required supporting documents?\"","timestamp":"2025-08-04T17:01:10.481Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合免费维修的条件。能否请您详细说明一下保修范围和需要提供的证明材料？\"","timestamp":"2025-08-04T17:01:10.481Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I would like to inquire about the specific warranty policy for the product. My device has recently encountered some issues, and I'm not entirely sure if they fall under the warranty coverage. Additionally, if repairs are needed, approximately how long would it take to resolve? Thank you.\"\"","timestamp":"2025-08-04T17:01:10.669Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不太确定是否符合保修范围。另外，如果需要维修的话，大概需要多长时间能处理好？谢谢。\"\"","timestamp":"2025-08-04T17:01:10.669Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific warranty policy for your products. My device has recently encountered some issues, and I'm not sure if they're covered under warranty. Additionally, if repairs are needed, approximately how long would the process take?\"\"","timestamp":"2025-08-04T17:01:10.675Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体政策。我的设备最近出现了一些问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好呢？\"\"","timestamp":"2025-08-04T17:01:10.675Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，主要取决于以下几个因素：\n\n1. 保修期限：通常为购买后1-2年（具体以您的购买凭证或产品说明书为准）\n2. 保修范围：非人为损坏的性能故障可免费维修\n3. 所需凭证：建议您准...\"","timestamp":"2025-08-04T17:01:10.869Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326848988_d0qmo9ck3 in 21883ms","timestamp":"2025-08-04T17:01:10.873Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修条款和维修流程：\n\n1. 保修条款：\n- 通常提供1-2年有限保修（具体以产品说明书为准）\n- 覆盖范围：非人为损坏的质量问题\n- 不包含：意外损坏、不当使用或自行拆修\n\n2. 维修...\"","timestamp":"2025-08-04T17:01:11.937Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326847665_3uvha71j9 in 24278ms","timestamp":"2025-08-04T17:01:11.945Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！很高兴为您解答。为了能给您最准确的回答，我需要了解：\n\n1. 您具体指的是哪个功能？（例如：某个软件/设备/平台的功能名称）\n2. 您当前遇到的具体问题是什么？\n\n这样我可以更有针对性地为您提供...\"","timestamp":"2025-08-04T17:01:12.030Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326859982_d0vlqfsoq in 12000ms","timestamp":"2025-08-04T17:01:12.035Z"}
{"context":"TranslationService","level":"info","message":"DeepSeek translation result: \"\"Hello, I'd like to inquire about the specific terms of the product warranty. My device has recently had some minor issues, and I'm not sure if they're covered under the warranty. Also, if repairs are needed, approximately how long would it take to resolve?\"\"","timestamp":"2025-08-04T17:01:12.133Z"}
{"context":"TranslationService","level":"info","message":"Generating AI answer for: \"\"您好，我想咨询一下关于产品保修的具体条款。我的设备最近出现了一些小问题，不知道是否在保修范围内？另外，如果需要维修的话，大概需要多长时间能处理好？\"\"","timestamp":"2025-08-04T17:01:12.133Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，主要取决于以下几个因素：\n\n1. 保修期限：通常为购买后1-2年（具体以您的购买凭证或产品说明书为准）\n\n2. 保修范围：\n- 非人为损坏\n- 正常使用情况下出现的故障\n- 未...\"","timestamp":"2025-08-04T17:01:12.151Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326850966_jqygoxhqv in 21191ms","timestamp":"2025-08-04T17:01:12.160Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，主要信息如下：\n\n1. 保修条件：\n- 通常提供1-2年有限保修（具体以您的购买凭证为准）\n- 非人为损坏且在保修期内可免费维修\n- 需提供有效购买凭证和保修卡\n\n2. 不符合...\"","timestamp":"2025-08-04T17:01:12.455Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326848027_4x554q92m in 24429ms","timestamp":"2025-08-04T17:01:12.459Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，我为您简要说明：\n\n1. 保修范围：\n- 通常覆盖产品本身在正常使用情况下出现的非人为损坏\n- 一般保修期为1年（以购买发票日期为准）\n\n2. 免费维修条件：\n- 在保修期内\n...\"","timestamp":"2025-08-04T17:01:13.212Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326847365_ratcph0ot in 25847ms","timestamp":"2025-08-04T17:01:13.216Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，主要取决于以下几个因素：\n\n1. 保修期限：通常为购买后1-2年（具体请查看您的购买凭证或产品说明书）\n\n2. 保修范围：\n- 非人为损坏\n- 正常使用情况下出现的质量问题\n-...\"","timestamp":"2025-08-04T17:01:13.307Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326849646_xq5pvz0ik in 23668ms","timestamp":"2025-08-04T17:01:13.315Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，主要信息如下：\n\n1. 保修范围：\n- 通常覆盖1年内的非人为损坏\n- 包括制造缺陷和材料质量问题\n- 不包含意外损坏或自行拆修的情况\n\n2. 所需材料：\n- 购买凭证（发票/...\"","timestamp":"2025-08-04T17:01:13.763Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326850665_3l05c04ip in 23127ms","timestamp":"2025-08-04T17:01:13.795Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，我为您简要说明：\n\n1. 保修范围：\n- 通常提供1-2年有限保修（具体以您购买时的保修卡为准）\n- 覆盖非人为损坏的硬件故障\n\n2. 所需材料：\n√ 购买凭证/发票\n√ 产品...\"","timestamp":"2025-08-04T17:01:13.912Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326848686_fpjbuuytu in 25231ms","timestamp":"2025-08-04T17:01:13.919Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，我为您简要说明如下：\n\n1. 保修范围：\n- 通常覆盖产品自身质量问题导致的故障\n- 不包含人为损坏、自然灾害或未经授权的拆修\n- 具体保修期请查看产品说明书(通常1-2年)\n...\"","timestamp":"2025-08-04T17:01:13.968Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326850305_r79tgjjgu in 23668ms","timestamp":"2025-08-04T17:01:13.974Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，我为您简要说明：\n\n1. 保修条件：\n- 通常提供1-2年有限保修（具体以您购买时的保修卡为准）\n- 非人为损坏的质量问题可免费维修\n- 需提供有效购买凭证\n\n2. 不在保修范...\"","timestamp":"2025-08-04T17:01:14.175Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326847067_pvcre2an6 in 27113ms","timestamp":"2025-08-04T17:01:14.183Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策：\n\n1. 保修条件：通常提供1年免费保修（以购买发票日期为准），非人为损坏且在正常使用情况下出现的问题都符合保修范围。\n\n2. 维修费用：若超出保修期或属人为损坏，费用需根据具...\"","timestamp":"2025-08-04T17:01:15.245Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326853304_6svrwnbdw in 21947ms","timestamp":"2025-08-04T17:01:15.252Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，我为您简要说明：\n\n1. 保修条件：\n- 通常提供1-2年有限保修（具体以您的购买凭证为准）\n- 需提供有效购买凭证和保修卡\n- 非人为损坏或质量问题可享免费维修\n\n2. 不在...\"","timestamp":"2025-08-04T17:01:15.764Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326849344_5bjrerxvj in 26425ms","timestamp":"2025-08-04T17:01:15.770Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修的问题，我为您简要说明：\n\n1. 保修条件：\n- 通常保修期为1年（以购买凭证日期为准）\n- 需是非人为损坏的质量问题\n- 需提供完整购买凭证和保修卡\n\n2. 费用参考：\n- 保修期...\"","timestamp":"2025-08-04T17:01:16.139Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326852285_pr2ov52v3 in 23859ms","timestamp":"2025-08-04T17:01:16.145Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，我为您简要说明：\n\n1. 保修条件：\n- 通常提供1-2年有限保修（具体以您的购买凭证为准）\n- 非人为损坏且在保修期内可免费维修\n- 需提供有效购买凭证和保修卡\n\n2. 不在...\"","timestamp":"2025-08-04T17:01:16.283Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326851325_b0lrp6rfb in 24963ms","timestamp":"2025-08-04T17:01:16.291Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！感谢您对我们新套餐的关注。以下是该套餐的主要信息：\n\n1. 资费标准：\n- 月费：128元\n- 合约期：12个月（可续约）\n\n2. 包含内容：\n- 国内流量：60GB（含5G网络）\n- 国内通话...\"","timestamp":"2025-08-04T17:01:16.314Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326849947_1gqx94ec6 in 26373ms","timestamp":"2025-08-04T17:01:16.321Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修服务，我为您简要说明如下：\n\n1. 保修范围：\n- 通常涵盖产品本身在正常使用情况下出现的非人为故障\n- 不包含意外损坏、私自拆修或自然灾害造成的损坏\n- 具体以您购买时的保修卡/说...\"","timestamp":"2025-08-04T17:01:16.578Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326848386_puzgk3tr6 in 28195ms","timestamp":"2025-08-04T17:01:16.582Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修条款，主要信息如下：\n\n1. 保修范围：\n- 正常使用情况下出现的非人为损坏\n- 原厂配件及工艺问题\n- 保修期内(通常1-2年，以购买凭证为准)\n\n2. 不包含：\n- 人为损坏或不...\"","timestamp":"2025-08-04T17:01:16.752Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修条款，主要信息如下：\n\n1. 保修范围：\n- 通常覆盖1年内的非人为损坏\n- 包括制造缺陷和材料质量问题\n- 不包含意外损坏、私自拆修或不当使用\n\n2. 所需材料：\n- 购买凭证（发...\"","timestamp":"2025-08-04T17:01:16.755Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326851925_7i9xj9kxz in 24829ms","timestamp":"2025-08-04T17:01:16.757Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326855647_b1xwm3nu2 in 21120ms","timestamp":"2025-08-04T17:01:16.769Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修服务，我为您简要说明：\n\n1. 保修条款：\n- 通常提供1-2年有限保修（具体以您购买时的保修卡为准）\n- 人为损坏或非质量问题通常不在保修范围内\n- 建议您先查看产品说明书内的保修...\"","timestamp":"2025-08-04T17:01:17.399Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326852946_yoic15xyt in 24455ms","timestamp":"2025-08-04T17:01:17.403Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，我为您简要说明如下：\n\n1. 保修范围：\n- 通常覆盖产品正常使用情况下出现的非人为损坏\n- 一般保修期为1年（以购买凭证日期为准）\n- 不包含：人为损坏、自然灾害、自行拆修等...\"","timestamp":"2025-08-04T17:01:17.590Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326852587_031srdjn4 in 25005ms","timestamp":"2025-08-04T17:01:17.594Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，主要信息如下：\n\n1. 保修范围：通常自购买日起1年内，非人为损坏的性能故障都在保修范围内（具体以您的购买凭证日期为准）\n\n2. 维修时长：常规维修约需5-7个工作日（不含物流...\"","timestamp":"2025-08-04T17:01:17.952Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326854326_mwxjwzzoy in 23630ms","timestamp":"2025-08-04T17:01:17.957Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，我为您简要说明如下：\n\n1. 保修范围：\n- 通常覆盖产品本身在正常使用情况下出现的非人为损坏\n- 一般保修期为1-2年（具体以您的购买凭证为准）\n- 不包含：人为损坏、自然灾...\"","timestamp":"2025-08-04T17:01:18.279Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326854988_tx02bkq5t in 23291ms","timestamp":"2025-08-04T17:01:18.284Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，我为您简要说明如下：\n\n1. 保修范围：\n- 通常覆盖产品说明书列明的功能故障\n- 非人为损坏的硬件问题\n- 正常使用情况下出现的性能故障\n\n2. 不包含的情况：\n- 人为损坏...\"","timestamp":"2025-08-04T17:01:19.306Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326855347_8pby574yu in 23965ms","timestamp":"2025-08-04T17:01:19.314Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修期的问题，建议您：\n\n1. 通常保修期在产品包装盒或保修卡上会有明确标注\n2. 不同产品类别的保修期可能不同（如电子产品通常1-2年）\n3. 最准确的方式是：\n   - 查看购买凭证...\"","timestamp":"2025-08-04T17:01:19.882Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326859726_e7crnzx8u in 20163ms","timestamp":"2025-08-04T17:01:19.889Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，我为您简要说明：\n\n1. 保修条件：\n- 通常提供1-2年有限保修（具体以您的购买凭证为准）\n- 需提供有效购买凭证\n- 非人为损坏或质量问题可享免费维修\n\n2. 不在保修范围...\"","timestamp":"2025-08-04T17:01:20.030Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326854686_clrg0rb4x in 25347ms","timestamp":"2025-08-04T17:01:20.036Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，主要信息如下：\n\n1. 保修范围：\n- 通常覆盖非人为损坏的硬件故障\n- 自购买日起1年内有效（具体以您的购买凭证为准）\n- 不包含意外损坏或自行拆机的情况\n\n2. 维修时长：...\"","timestamp":"2025-08-04T17:01:20.255Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326853606_m35vdty5l in 26651ms","timestamp":"2025-08-04T17:01:20.259Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修条款，主要信息如下：\n\n1. 保修范围：\n- 正常使用情况下出现的非人为损坏\n- 产品本身的质量问题\n- 保修期通常为1年（以购买凭证日期为准）\n\n2. 不包含的情况：\n- 人为损坏...\"","timestamp":"2025-08-04T17:01:20.360Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326856965_ypdary4ak in 23401ms","timestamp":"2025-08-04T17:01:20.366Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，我为您简要说明：\n\n1. 保修范围：\n- 正常使用情况下出现的非人为故障\n- 在保修期内(通常1-2年，具体以购买凭证为准)\n- 未经私自拆修的产品\n\n2. 不保修情况：\n- ...\"","timestamp":"2025-08-04T17:01:20.639Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326853965_as7o8lahg in 26679ms","timestamp":"2025-08-04T17:01:20.647Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，通常包含以下要点：\n\n1. 保修条件：\n- 一般提供1-2年有限保修（具体以您的购买凭证为准）\n- 需提供有效购买凭证\n- 非人为损坏可享免费维修\n\n2. 费用说明：\n- 保修...\"","timestamp":"2025-08-04T17:01:20.743Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326856305_q5d5vqh0f in 24442ms","timestamp":"2025-08-04T17:01:20.747Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，主要分为以下两种情况：\n\n1. 免费保修条件：\n- 设备在保修期内（通常为1-2年，具体以购买凭证为准）\n- 非人为损坏（如非进水、摔坏等）\n- 属于正常使用出现的性能故障\n\n...\"","timestamp":"2025-08-04T17:01:20.974Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326857687_spxi78u0h in 23290ms","timestamp":"2025-08-04T17:01:20.978Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，主要信息如下：\n\n1. 保修条件：\n- 通常提供1年有限保修（具体以购买凭证日期为准）\n- 非人为损坏或质量问题可保修\n- 需提供原始购买凭证和完整产品\n\n2. 维修周期：\n-...\"","timestamp":"2025-08-04T17:01:21.133Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326856005_qagff3rds in 25132ms","timestamp":"2025-08-04T17:01:21.140Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，我为您简要说明：\n\n1. 保修范围：通常产品提供1-2年有限保修（具体以您的购买凭证为准），覆盖非人为损坏的硬件故障。\n\n2. 维修时效：常规维修通常需要5-7个工作日（不含物...\"","timestamp":"2025-08-04T17:01:21.221Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326857325_cqgg05tmx in 23905ms","timestamp":"2025-08-04T17:01:21.231Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，主要信息如下：\n\n1. 保修范围：\n- 通常覆盖产品本身在正常使用情况下出现的非人为损坏\n- 一般保修期为1年（以购买凭证日期为准）\n- 不包含：人为损坏、自然灾害、自行拆修等...\"","timestamp":"2025-08-04T17:01:21.643Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326858765_8g2ir19sl in 22881ms","timestamp":"2025-08-04T17:01:21.647Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，主要分为以下几点：\n\n1. 保修条件：\n- 通常提供1年免费保修（以购买凭证日期为准）\n- 非人为损坏的质量问题可免费维修\n- 进水、摔坏等人为损坏不在保修范围内\n\n2. 费用...\"","timestamp":"2025-08-04T17:01:21.831Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326856605_zvqwdp8qn in 25231ms","timestamp":"2025-08-04T17:01:21.836Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，主要信息如下：\n\n1. 保修范围：\n- 通常覆盖产品本身的质量问题\n- 人为损坏或自然灾害不在保修范围内\n- 保修期一般为1-2年(具体以购买凭证为准)\n\n2. 维修周期：\n-...\"","timestamp":"2025-08-04T17:01:22.413Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326858047_bxwfjt1my in 24369ms","timestamp":"2025-08-04T17:01:22.418Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，通常包含以下要点(具体以您的购买凭证或产品说明书为准)：\n\n1. 保修范围：\n- 一般提供1-2年有限保修\n- 覆盖非人为损坏的硬件故障\n- 不包含意外损坏或自行拆修的情况\n\n...\"","timestamp":"2025-08-04T17:01:22.597Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326859425_uu1zh1qdm in 23176ms","timestamp":"2025-08-04T17:01:22.602Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修政策，我为您简要说明如下：\n\n1. 保修范围：\n- 通常覆盖产品正常使用情况下出现的非人为损坏\n- 包括制造缺陷和材料质量问题\n- 不包含意外损坏、私自拆修或不当使用造成的问题\n\n2...\"","timestamp":"2025-08-04T17:01:22.794Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326858407_0cta90hwl in 24391ms","timestamp":"2025-08-04T17:01:22.799Z"}
{"context":"TranslationService","level":"info","message":"Generated AI answer: \"您好！关于产品保修的问题：\n\n1. 保修范围：\n- 通常保修期为1年（以购买凭证日期为准）\n- 覆盖非人为损坏的硬件故障\n- 不包含外观损伤或液体浸入等情况\n\n2. 维修时长：\n- 常规问题：3-5个...\"","timestamp":"2025-08-04T17:01:23.877Z"}
{"context":"TranslationGateway","level":"info","message":"Voice processing completed for session session_1754326859126_pvu5ljuwq in 24755ms","timestamp":"2025-08-04T17:01:23.881Z"}
{"context":"TranslationGateway","level":"info","message":"Client disconnected: <EMAIL> (l1Zbyi45ytKPjP3KAAAE)","timestamp":"2025-08-04T17:04:26.608Z"}
{"context":"TranslationGateway","level":"info","message":"Client disconnected: <EMAIL> (e3y_Av6TjcAd10fXAAAB)","timestamp":"2025-08-04T17:34:52.804Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translate\" message","timestamp":"2025-08-04T17:56:26.413Z"}
