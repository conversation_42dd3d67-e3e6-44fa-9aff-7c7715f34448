"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TranslationGateway_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranslationGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const common_1 = require("@nestjs/common");
const translation_service_1 = require("./translation.service");
const jwt_1 = require("@nestjs/jwt");
const speech_service_1 = require("../speech/speech.service");
let TranslationGateway = TranslationGateway_1 = class TranslationGateway {
    constructor(translationService, jwtService, speechService) {
        this.translationService = translationService;
        this.jwtService = jwtService;
        this.speechService = speechService;
        this.logger = new common_1.Logger(TranslationGateway_1.name);
    }
    async handleConnection(client) {
        try {
            const token = client.handshake.auth?.token ||
                client.handshake.headers?.authorization?.replace('Bearer ', '') ||
                client.handshake.query?.token;
            if (!token) {
                this.logger.warn(`Connection rejected: No token provided (${client.id}) from ${client.handshake.address}`);
                client.emit('error', {
                    message: '认证失败：缺少token',
                    code: 'NO_TOKEN',
                    timestamp: new Date()
                });
                client.disconnect();
                return;
            }
            const payload = this.jwtService.verify(token);
            client.userId = payload.sub;
            client.userEmail = payload.email;
            this.logger.log(`Client connected: ${client.userEmail} (${client.id}) from ${client.handshake.address}`);
            client.emit('authenticated', {
                userId: client.userId,
                email: client.userEmail,
                timestamp: new Date(),
                serverTime: new Date().toISOString()
            });
            client.emit('server_status', {
                speechRecognition: 'available',
                translation: 'available',
                aiQA: 'available',
                timestamp: new Date()
            });
        }
        catch (error) {
            this.logger.error(`Authentication failed for client ${client.id}:`, error);
            client.emit('error', {
                message: '认证失败：token无效或已过期',
                code: 'INVALID_TOKEN',
                timestamp: new Date()
            });
            client.disconnect();
        }
    }
    handleDisconnect(client) {
        this.logger.log(`Client disconnected: ${client.userEmail} (${client.id})`);
    }
    async handleTranslate(data, client) {
        try {
            if (!client.userId) {
                client.emit('error', { message: '用户未认证' });
                return;
            }
            this.logger.log(`Translation request from ${client.userEmail}: ${data.text}`);
            const request = {
                text: data.text,
                sourceLanguage: data.sourceLanguage,
                targetLanguage: data.targetLanguage,
                userId: client.userId,
            };
            const result = await this.translationService.translateText(request);
            client.emit('translation_result', {
                id: Date.now().toString(),
                originalText: result.originalText,
                translatedText: result.translatedText,
                sourceLanguage: result.sourceLanguage,
                targetLanguage: result.targetLanguage,
                timestamp: new Date(),
                isQuestion: result.isQuestion,
            });
            if (result.isQuestion && result.aiAnswer) {
                client.emit('question_answer', {
                    id: Date.now().toString(),
                    question: result.originalText,
                    answer: result.aiAnswer,
                    timestamp: new Date(),
                });
            }
        }
        catch (error) {
            this.logger.error('Translation error:', error);
            client.emit('error', { message: error.message || '翻译失败' });
        }
    }
    async handleVoiceStream(data, client) {
        const startTime = Date.now();
        const sessionId = data.sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        try {
            if (!client.userId) {
                client.emit('error', {
                    message: '用户未认证',
                    code: 'UNAUTHORIZED',
                    sessionId,
                    timestamp: new Date()
                });
                return;
            }
            if (!data.audioData || !data.language) {
                client.emit('error', {
                    message: '缺少必要的音频数据或语言参数',
                    code: 'INVALID_INPUT',
                    sessionId,
                    timestamp: new Date()
                });
                return;
            }
            this.logger.log(`Processing voice stream from ${client.userEmail}, language: ${data.language}, sessionId: ${sessionId}`);
            client.emit('voice_processing_start', {
                sessionId,
                language: data.language,
                timestamp: new Date()
            });
            let audioBuffer;
            try {
                audioBuffer = Buffer.from(data.audioData, 'base64');
                if (audioBuffer.length === 0) {
                    throw new Error('Empty audio data');
                }
                if (audioBuffer.length > 10 * 1024 * 1024) {
                    throw new Error('Audio data too large');
                }
            }
            catch (error) {
                this.logger.error('Failed to decode audio data:', error);
                client.emit('error', {
                    message: '音频数据格式错误或过大',
                    code: 'INVALID_AUDIO_DATA',
                    sessionId,
                    timestamp: new Date()
                });
                return;
            }
            const speechStartTime = Date.now();
            const speechResult = await this.speechService.speechToText(audioBuffer, data.language);
            const speechDuration = Date.now() - speechStartTime;
            if (!speechResult.text || speechResult.text.trim().length === 0) {
                this.logger.warn(`No speech detected in audio for session ${sessionId}`);
                client.emit('voice_processing_complete', {
                    sessionId,
                    success: false,
                    message: '未检测到语音内容',
                    processingTime: Date.now() - startTime,
                    timestamp: new Date()
                });
                return;
            }
            this.logger.log(`Speech recognition result: "${speechResult.text}" (confidence: ${speechResult.confidence}, duration: ${speechDuration}ms)`);
            client.emit('speech_recognition_result', {
                text: speechResult.text,
                language: data.language,
                confidence: speechResult.confidence,
                sessionId,
                processingTime: speechDuration,
                timestamp: new Date(),
            });
            const targetLanguage = data.language === 'zh-CN' ? 'en-US' : 'zh-CN';
            const request = {
                text: speechResult.text,
                sourceLanguage: data.language,
                targetLanguage: targetLanguage,
                userId: client.userId,
            };
            const translationStartTime = Date.now();
            const result = await this.translationService.translateText(request);
            const translationDuration = Date.now() - translationStartTime;
            client.emit('translation_result', {
                id: Date.now().toString(),
                originalText: result.originalText,
                translatedText: result.translatedText,
                sourceLanguage: result.sourceLanguage,
                targetLanguage: result.targetLanguage,
                timestamp: new Date(),
                isQuestion: result.isQuestion,
                confidence: speechResult.confidence,
                sessionId,
                processingTime: translationDuration,
            });
            if (result.isQuestion && result.aiAnswer) {
                client.emit('question_answer', {
                    id: Date.now().toString(),
                    question: result.originalText,
                    answer: result.aiAnswer,
                    sessionId,
                    timestamp: new Date(),
                });
            }
            const totalDuration = Date.now() - startTime;
            client.emit('voice_processing_complete', {
                sessionId,
                success: true,
                totalProcessingTime: totalDuration,
                speechRecognitionTime: speechDuration,
                translationTime: translationDuration,
                timestamp: new Date()
            });
            this.logger.log(`Voice processing completed for session ${sessionId} in ${totalDuration}ms`);
        }
        catch (error) {
            const totalDuration = Date.now() - startTime;
            this.logger.error(`Voice stream error for session ${sessionId}:`, error);
            client.emit('error', {
                message: '语音处理失败: ' + (error.message || '未知错误'),
                code: 'VOICE_PROCESSING_ERROR',
                sessionId,
                processingTime: totalDuration,
                timestamp: new Date()
            });
            client.emit('voice_processing_complete', {
                sessionId,
                success: false,
                error: error.message,
                processingTime: totalDuration,
                timestamp: new Date()
            });
        }
    }
    async handleGetHistory(data, client) {
        try {
            if (!client.userId) {
                client.emit('error', { message: '用户未认证' });
                return;
            }
            const history = await this.translationService.findByUser(client.userId, data.limit || 20);
            client.emit('translation_history', history);
        }
        catch (error) {
            this.logger.error('Get history error:', error);
            client.emit('error', { message: '获取历史记录失败' });
        }
    }
};
exports.TranslationGateway = TranslationGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], TranslationGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('translate'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TranslationGateway.prototype, "handleTranslate", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('voice_stream'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TranslationGateway.prototype, "handleVoiceStream", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('get_translation_history'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TranslationGateway.prototype, "handleGetHistory", null);
exports.TranslationGateway = TranslationGateway = TranslationGateway_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: [
                'http://localhost:3000',
                'http://localhost:3001',
                'http://localhost:5173',
                'http://localhost:5174',
                'http://localhost:5175',
                'http://localhost:5176',
                'http://localhost:5177',
                'http://localhost:5178',
            ],
            credentials: true,
        },
        namespace: '/translation',
        transports: ['websocket', 'polling'],
        pingTimeout: 60000,
        pingInterval: 25000,
    }),
    __metadata("design:paramtypes", [translation_service_1.TranslationService,
        jwt_1.JwtService,
        speech_service_1.SpeechService])
], TranslationGateway);
//# sourceMappingURL=translation.gateway.js.map