import { PaymentMethod } from '../entities/payment.entity';
export interface PaymentProcessor {
    readonly paymentMethod: PaymentMethod;
    createPayment(params: CreatePaymentParams): Promise<CreatePaymentResult>;
    verifyCallback(callbackData: any): Promise<CallbackVerificationResult>;
    queryPaymentStatus(thirdPartyOrderId: string): Promise<PaymentStatusResult>;
    refund(params: RefundParams): Promise<RefundResult>;
}
export interface CreatePaymentParams {
    orderNumber: string;
    amount: number;
    currency: string;
    description: string;
    userId: string;
    notifyUrl: string;
    returnUrl?: string;
    extraParams?: Record<string, any>;
}
export interface CreatePaymentResult {
    success: boolean;
    thirdPartyOrderId?: string;
    paymentUrl?: string;
    paymentForm?: string;
    error?: string;
    extraData?: Record<string, any>;
}
export interface CallbackVerificationResult {
    verified: boolean;
    thirdPartyOrderId?: string;
    paymentStatus?: 'success' | 'failed' | 'pending';
    amount?: number;
    error?: string;
    rawData?: any;
}
export interface PaymentStatusResult {
    success: boolean;
    status?: 'success' | 'failed' | 'pending' | 'cancelled';
    amount?: number;
    paidAt?: Date;
    error?: string;
}
export interface RefundParams {
    thirdPartyOrderId: string;
    refundAmount: number;
    reason: string;
    refundOrderNumber: string;
}
export interface RefundResult {
    success: boolean;
    thirdPartyRefundId?: string;
    error?: string;
}
