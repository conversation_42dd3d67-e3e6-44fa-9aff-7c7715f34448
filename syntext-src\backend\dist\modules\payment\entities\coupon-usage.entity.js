"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CouponUsage = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const user_entity_1 = require("../../user/entities/user.entity");
const coupon_entity_1 = require("../../coupon/entities/coupon.entity");
const payment_entity_1 = require("./payment.entity");
let CouponUsage = class CouponUsage {
};
exports.CouponUsage = CouponUsage;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '使用记录ID' }),
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], CouponUsage.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], CouponUsage.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码ID' }),
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], CouponUsage.prototype, "couponId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付ID', required: false }),
    (0, typeorm_1.Column)('uuid', { nullable: true }),
    __metadata("design:type", String)
], CouponUsage.prototype, "paymentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码' }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CouponUsage.prototype, "couponCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣金额' }),
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], CouponUsage.prototype, "discountAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '原始金额' }),
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], CouponUsage.prototype, "originalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最终金额' }),
    (0, typeorm_1.Column)('decimal', { precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], CouponUsage.prototype, "finalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '使用时间' }),
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CouponUsage.prototype, "usedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.id),
    (0, typeorm_1.JoinColumn)({ name: 'userId' }),
    __metadata("design:type", user_entity_1.User)
], CouponUsage.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => coupon_entity_1.Coupon, (coupon) => coupon.id),
    (0, typeorm_1.JoinColumn)({ name: 'couponId' }),
    __metadata("design:type", coupon_entity_1.Coupon)
], CouponUsage.prototype, "coupon", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => payment_entity_1.Payment, (payment) => payment.id, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'paymentId' }),
    __metadata("design:type", payment_entity_1.Payment)
], CouponUsage.prototype, "payment", void 0);
exports.CouponUsage = CouponUsage = __decorate([
    (0, typeorm_1.Entity)('coupon_usages')
], CouponUsage);
//# sourceMappingURL=coupon-usage.entity.js.map