{"version": 3, "file": "speech.service.js", "sourceRoot": "", "sources": ["../../../src/modules/speech/speech.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AASxC,IAAM,aAAa,qBAAnB,MAAM,aAAa;IAIxB,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAH/B,WAAM,GAAG,IAAI,eAAM,CAAC,eAAa,CAAC,IAAI,CAAC,CAAC;QAIvD,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAClC,IAAI,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAClE,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAElE,IAAI,cAAc,IAAI,cAAc,EAAE,CAAC;gBACrC,IAAI,CAAC,YAAY,GAAG;oBAClB,MAAM,EAAE,cAAc;oBACtB,MAAM,EAAE,cAAc;iBACvB,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;YAClE,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;gBAC7E,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAC3B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;YACzE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;YACrE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,SAAiB,EAAE,WAAmB,OAAO;QAC9D,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACN,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAEnD,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,SAAiB,EAAE,QAAgB;QACpE,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,SAAS,CAAC,MAAM,qBAAqB,QAAQ,EAAE,CAAC,CAAC;YAIpG,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YAC3D,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,iCAAiC,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YAE5F,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;YAE3D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,aAAa,kBAAkB,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAErG,OAAO;gBACL,IAAI,EAAE,aAAa;gBACnB,UAAU,EAAE,UAAU;gBACtB,QAAQ,EAAE,QAAQ;aACnB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,SAAiB;QAE5C,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;QAChC,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;QAC/D,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACzD,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAE5D,OAAO;YACL,MAAM;YACN,YAAY;YACZ,WAAW;YACX,cAAc;YACd,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC;SAChD,CAAC;IACJ,CAAC;IAEO,yBAAyB,CAAC,SAAiB;QACjD,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7C,MAAM,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACxC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1B,CAAC;QACD,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACtC,CAAC;IAEO,oBAAoB,CAAC,SAAiB;QAC5C,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7C,MAAM,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,IAAI,MAAM,GAAG,MAAM,CAAC;QAC5B,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IAEO,oBAAoB,CAAC,SAAiB;QAC5C,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,MAAM,SAAS,GAAG,IAAI,CAAC;QAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,IAAI,MAAM,GAAG,SAAS,EAAE,CAAC;gBACvB,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,OAAO,YAAY,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC/C,CAAC;IAEO,mBAAmB,CAAC,SAAiB;QAE3C,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7C,MAAM,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,GAAG,EAAE,CAAC;gBACxC,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,UAAU,GAAG,MAAM,CAAC;QACtB,CAAC;QAED,OAAO,OAAO,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC1C,CAAC;IAEO,KAAK,CAAC,iCAAiC,CAAC,QAAa,EAAE,QAAgB;QAC7E,IAAI,CAAC;YAEH,MAAM,iBAAiB,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAEpD,MAAM,MAAM,GAAG,qBAAqB,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;;QAEjE,QAAQ,CAAC,MAAM;QACf,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;QAChC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/B,CAAC,QAAQ,CAAC,cAAc,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;OAC3C,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;;;;qBAIhB,CAAC;YAEhB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,mBAAmB,EAAE;gBAC3E,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;oBAClC,eAAe,EAAE,UAAU,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;iBACtD;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,KAAK,EAAE,eAAe;oBACtB,QAAQ,EAAE;wBACR;4BACE,IAAI,EAAE,QAAQ;4BACd,OAAO,EAAE,mCAAmC;yBAC7C;wBACD;4BACE,IAAI,EAAE,MAAM;4BACZ,OAAO,EAAE,MAAM;yBAChB;qBACF;oBACD,UAAU,EAAE,GAAG;oBACf,WAAW,EAAE,GAAG;iBACjB,CAAC;aACH,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,uBAAuB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YAC5D,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACnC,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;YAElE,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAC3D,CAAC;YAED,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;YAE5E,OAAO,IAAI,CAAC,8BAA8B,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAEO,8BAA8B,CAAC,QAAa,EAAE,QAAgB;QACpE,MAAM,iBAAiB,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAGpD,IAAI,QAAQ,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YAE3B,OAAO,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;QAC5C,CAAC;aAAM,IAAI,QAAQ,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YAElC,IAAI,QAAQ,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;gBAC9B,OAAO,iBAAiB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,oBAAoB,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,OAAO,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,yBAAyB,CAAC;YAClE,CAAC;QACH,CAAC;aAAM,CAAC;YAEN,IAAI,QAAQ,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC;gBAChC,OAAO,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,8CAA8C,CAAC;YAChG,CAAC;iBAAM,CAAC;gBACN,OAAO,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,4CAA4C,CAAC;YAC7F,CAAC;QACH,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,QAAa;QAEvC,IAAI,UAAU,GAAG,GAAG,CAAC;QAGrB,IAAI,QAAQ,CAAC,MAAM,GAAG,IAAI;YAAE,UAAU,IAAI,GAAG,CAAC;QAC9C,IAAI,QAAQ,CAAC,MAAM,GAAG,IAAI;YAAE,UAAU,IAAI,GAAG,CAAC;QAG9C,IAAI,QAAQ,CAAC,WAAW,GAAG,GAAG;YAAE,UAAU,IAAI,GAAG,CAAC;QAClD,IAAI,QAAQ,CAAC,WAAW,GAAG,IAAI;YAAE,UAAU,IAAI,GAAG,CAAC;QAGnD,IAAI,QAAQ,CAAC,cAAc,GAAG,GAAG,IAAI,QAAQ,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC;YACnE,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;QAGD,IAAI,QAAQ,CAAC,UAAU,GAAG,IAAI,IAAI,QAAQ,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;YAC5D,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;IACnD,CAAC;IAIO,KAAK,CAAC,gBAAgB,CAAC,SAAiB,EAAE,QAAgB;QAEhE,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;QAG7E,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC;QACrC,MAAM,iBAAiB,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAGpD,IAAI,SAAoE,CAAC;QAEzE,IAAI,iBAAiB,EAAE,CAAC;YACtB,SAAS,GAAG;gBAEV,EAAE,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAC9D,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE;gBAC1D,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE;gBAC3D,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE;gBAGzD,EAAE,IAAI,EAAE,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAChE,EAAE,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAC7D,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAC1D,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAG5D,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAC/D,EAAE,IAAI,EAAE,eAAe,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAClE,EAAE,IAAI,EAAE,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAG/D,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE;gBAC5D,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE;gBAC9D,EAAE,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE;aAC/D,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,SAAS,GAAG;gBAEV,EAAE,IAAI,EAAE,yBAAyB,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAC5E,EAAE,IAAI,EAAE,kCAAkC,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE;gBACnF,EAAE,IAAI,EAAE,8BAA8B,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE;gBAChF,EAAE,IAAI,EAAE,yBAAyB,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE;gBAG1E,EAAE,IAAI,EAAE,sCAAsC,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE;gBACxF,EAAE,IAAI,EAAE,4BAA4B,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAC9E,EAAE,IAAI,EAAE,sBAAsB,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE;gBACxE,EAAE,IAAI,EAAE,0BAA0B,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAG5E,EAAE,IAAI,EAAE,+BAA+B,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAClF,EAAE,IAAI,EAAE,qDAAqD,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE;gBACxG,EAAE,IAAI,EAAE,+BAA+B,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE;gBAGjF,EAAE,IAAI,EAAE,gCAAgC,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE;gBACpF,EAAE,IAAI,EAAE,qCAAqC,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE;gBACzF,EAAE,IAAI,EAAE,0BAA0B,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE;aAC9E,CAAC;QACJ,CAAC;QAGD,IAAI,gBAAyE,CAAC;QAE9E,IAAI,WAAW,GAAG,IAAI,EAAE,CAAC;YAEvB,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACtF,gBAAgB,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/E,CAAC;aAAM,IAAI,WAAW,GAAG,IAAI,EAAE,CAAC;YAE9B,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACxF,gBAAgB,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;QACjF,CAAC;aAAM,CAAC;YAEN,gBAAgB,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7E,CAAC;QAGD,MAAM,cAAc,GAAG,IAAI,CAAC;QAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QAC9D,MAAM,eAAe,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACpD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,cAAc,GAAG,WAAW,GAAG,eAAe,CAAC,CAAC,CAAC;QAEjG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,gBAAgB,CAAC,IAAI,kBAAkB,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,gBAAgB,CAAC,QAAQ,iBAAiB,gBAAgB,CAAC,UAAU,GAAG,CAAC,CAAC;QAE3M,OAAO;YACL,IAAI,EAAE,gBAAgB,CAAC,IAAI;YAC3B,UAAU,EAAE,UAAU;YACtB,QAAQ,EAAE,QAAQ;SACnB,CAAC;IACJ,CAAC;IAGD,KAAK,CAAC,cAAc,CAAC,SAAiB;QACpC,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBAEtB,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;gBAEtD,MAAM,MAAM,GAAG;;QAEf,QAAQ,CAAC,MAAM;QACf,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;QAChC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;OAChC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;;mBAElB,CAAC;gBAEZ,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,mBAAmB,EAAE;oBAC3E,MAAM,EAAE,MAAM;oBACd,OAAO,EAAE;wBACP,cAAc,EAAE,kBAAkB;wBAClC,eAAe,EAAE,UAAU,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;qBACtD;oBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;wBACnB,KAAK,EAAE,eAAe;wBACtB,QAAQ,EAAE;4BACR;gCACE,IAAI,EAAE,MAAM;gCACZ,OAAO,EAAE,MAAM;6BAChB;yBACF;wBACD,UAAU,EAAE,EAAE;wBACd,WAAW,EAAE,GAAG;qBACjB,CAAC;iBACH,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;oBAChB,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACnC,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;oBAErE,IAAI,gBAAgB,KAAK,OAAO,IAAI,gBAAgB,KAAK,OAAO,EAAE,CAAC;wBACjE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,gBAAgB,EAAE,CAAC,CAAC;wBAC1D,OAAO,gBAAgB,CAAC;oBAC1B,CAAC;gBACH,CAAC;YACH,CAAC;YAGD,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,OAAO,CAAC;QACjB,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,qBAAqB,CAAC,UAAkB,EAAE,QAAgB;QAE9D,IAAI,UAAU,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;CACF,CAAA;AA9YY,sCAAa;wBAAb,aAAa;IADzB,IAAA,mBAAU,GAAE;qCAKwB,sBAAa;GAJrC,aAAa,CA8YzB"}