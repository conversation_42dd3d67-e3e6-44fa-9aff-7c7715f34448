"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var WechatProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WechatProcessor = void 0;
const common_1 = require("@nestjs/common");
const payment_entity_1 = require("../entities/payment.entity");
let WechatProcessor = WechatProcessor_1 = class WechatProcessor {
    constructor() {
        this.logger = new common_1.Logger(WechatProcessor_1.name);
        this.paymentMethod = payment_entity_1.PaymentMethod.WECHAT;
        this.config = {
            appId: process.env.WECHAT_APP_ID || 'mock_app_id',
            mchId: process.env.WECHAT_MCH_ID || 'mock_mch_id',
            apiKey: process.env.WECHAT_API_KEY || 'mock_api_key',
            certPath: process.env.WECHAT_CERT_PATH || '',
            keyPath: process.env.WECHAT_KEY_PATH || '',
            apiUrl: 'https://api.mch.weixin.qq.com',
        };
    }
    async createPayment(params) {
        try {
            this.logger.log(`Creating WeChat payment for order: ${params.orderNumber}`);
            if (this.isProductionConfigured()) {
                return await this.createRealPayment(params);
            }
            else {
                return await this.createMockPayment(params);
            }
        }
        catch (error) {
            this.logger.error('Failed to create WeChat payment:', error);
            return {
                success: false,
                error: '创建微信支付失败',
            };
        }
    }
    async verifyCallback(callbackData) {
        try {
            this.logger.log('Verifying WeChat callback');
            if (this.isProductionConfigured()) {
                return await this.verifyRealCallback(callbackData);
            }
            else {
                return await this.verifyMockCallback(callbackData);
            }
        }
        catch (error) {
            this.logger.error('Failed to verify WeChat callback:', error);
            return {
                verified: false,
                error: '回调验证失败',
            };
        }
    }
    async queryPaymentStatus(thirdPartyOrderId) {
        try {
            this.logger.log(`Querying WeChat payment status: ${thirdPartyOrderId}`);
            if (this.isProductionConfigured()) {
                return await this.queryRealPaymentStatus(thirdPartyOrderId);
            }
            else {
                return await this.queryMockPaymentStatus(thirdPartyOrderId);
            }
        }
        catch (error) {
            this.logger.error('Failed to query WeChat payment status:', error);
            return {
                success: false,
                error: '查询支付状态失败',
            };
        }
    }
    async refund(params) {
        try {
            this.logger.log(`Processing WeChat refund: ${params.refundOrderNumber}`);
            if (this.isProductionConfigured()) {
                return await this.processRealRefund(params);
            }
            else {
                return await this.processMockRefund(params);
            }
        }
        catch (error) {
            this.logger.error('Failed to process WeChat refund:', error);
            return {
                success: false,
                error: '退款处理失败',
            };
        }
    }
    isProductionConfigured() {
        return (this.config.appId !== 'mock_app_id' &&
            this.config.mchId !== 'mock_mch_id' &&
            this.config.apiKey !== 'mock_api_key');
    }
    async createRealPayment(params) {
        this.logger.warn('Real WeChat payment not implemented yet');
        return {
            success: false,
            error: '微信支付功能正在开发中，敬请期待',
        };
    }
    async createMockPayment(params) {
        const thirdPartyOrderId = `wechat_mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const paymentUrl = `https://mock-wechat.com/pay?order_id=${thirdPartyOrderId}&amount=${params.amount}`;
        this.logger.log(`Mock WeChat payment created: ${thirdPartyOrderId}`);
        return {
            success: true,
            thirdPartyOrderId,
            paymentUrl,
            extraData: {
                mockPayment: true,
                message: '这是模拟支付，实际不会扣费',
                qrCode: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`,
            },
        };
    }
    async verifyRealCallback(callbackData) {
        this.logger.warn('Real WeChat callback verification not implemented yet');
        return {
            verified: false,
            error: '回调验证功能正在开发中',
        };
    }
    async verifyMockCallback(callbackData) {
        if (callbackData && callbackData.result_code === 'SUCCESS') {
            return {
                verified: true,
                thirdPartyOrderId: callbackData.out_trade_no,
                paymentStatus: 'success',
                amount: parseInt(callbackData.total_fee),
                rawData: callbackData,
            };
        }
        return {
            verified: false,
            error: '模拟回调验证失败',
        };
    }
    async queryRealPaymentStatus(thirdPartyOrderId) {
        this.logger.warn('Real WeChat payment status query not implemented yet');
        return {
            success: false,
            error: '支付状态查询功能正在开发中',
        };
    }
    async queryMockPaymentStatus(thirdPartyOrderId) {
        if (thirdPartyOrderId.startsWith('wechat_mock_')) {
            const statuses = ['success', 'pending', 'failed'];
            const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
            return {
                success: true,
                status: randomStatus,
                amount: Math.floor(Math.random() * 10000) + 1000,
                paidAt: randomStatus === 'success' ? new Date() : undefined,
            };
        }
        return {
            success: false,
            error: '订单不存在',
        };
    }
    async processRealRefund(params) {
        this.logger.warn('Real WeChat refund not implemented yet');
        return {
            success: false,
            error: '退款功能正在开发中',
        };
    }
    async processMockRefund(params) {
        const thirdPartyRefundId = `wechat_refund_mock_${Date.now()}`;
        this.logger.log(`Mock WeChat refund processed: ${thirdPartyRefundId}`);
        return {
            success: true,
            thirdPartyRefundId,
        };
    }
};
exports.WechatProcessor = WechatProcessor;
exports.WechatProcessor = WechatProcessor = WechatProcessor_1 = __decorate([
    (0, common_1.Injectable)()
], WechatProcessor);
//# sourceMappingURL=wechat.processor.js.map