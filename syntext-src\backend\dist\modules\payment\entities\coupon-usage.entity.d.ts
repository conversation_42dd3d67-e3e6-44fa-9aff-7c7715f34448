import { User } from '../../user/entities/user.entity';
import { Coupon } from '../../coupon/entities/coupon.entity';
import { Payment } from './payment.entity';
export declare class CouponUsage {
    id: string;
    userId: string;
    couponId: string;
    paymentId?: string;
    couponCode: string;
    discountAmount: number;
    originalAmount: number;
    finalAmount: number;
    usedAt: Date;
    user: User;
    coupon: Coupon;
    payment?: Payment;
}
