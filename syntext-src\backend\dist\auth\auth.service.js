"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const user_service_1 = require("../modules/user/user.service");
const email_service_1 = require("./email.service");
let AuthService = class AuthService {
    constructor(userService, jwtService, emailService) {
        this.userService = userService;
        this.jwtService = jwtService;
        this.emailService = emailService;
        this.verificationCodes = new Map();
    }
    async sendVerificationCode(sendCodeDto) {
        const { email } = sendCodeDto;
        const existingCode = this.verificationCodes.get(email);
        if (existingCode && existingCode.expiresAt > new Date()) {
            throw new common_1.BadRequestException('验证码发送过于频繁，请稍后再试');
        }
        const code = Math.floor(100000 + Math.random() * 900000).toString();
        const expiresAt = new Date(Date.now() + 5 * 60 * 1000);
        this.verificationCodes.set(email, { code, expiresAt });
        await this.emailService.sendVerificationCode(email, code);
        return { message: '验证码已发送到您的邮箱' };
    }
    async verifyCodeAndLogin(verifyCodeDto) {
        const { email, code, betaCode } = verifyCodeDto;
        const BETA_CODE = 'weilynCHENliuYU';
        if (!betaCode || betaCode !== BETA_CODE) {
            throw new common_1.UnauthorizedException('内测验证码错误，请联系管理员获取正确的验证码');
        }
        const storedCode = this.verificationCodes.get(email);
        if (!storedCode) {
            throw new common_1.UnauthorizedException('验证码不存在或已过期');
        }
        if (storedCode.expiresAt < new Date()) {
            this.verificationCodes.delete(email);
            throw new common_1.UnauthorizedException('验证码已过期');
        }
        if (storedCode.code !== code) {
            throw new common_1.UnauthorizedException('验证码错误');
        }
        this.verificationCodes.delete(email);
        let user = await this.userService.findByEmail(email);
        if (!user) {
            user = await this.userService.create({ email });
        }
        await this.userService.updateLastLogin(user.id);
        const payload = { sub: user.id, email: user.email, role: user.role };
        const access_token = this.jwtService.sign(payload);
        return {
            access_token,
            user,
        };
    }
    async refreshToken(user) {
        const payload = { sub: user.id, email: user.email, role: user.role };
        const access_token = this.jwtService.sign(payload);
        return { access_token };
    }
    async getProfile(userId) {
        return await this.userService.findOne(userId);
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [user_service_1.UserService,
        jwt_1.JwtService,
        email_service_1.EmailService])
], AuthService);
//# sourceMappingURL=auth.service.js.map