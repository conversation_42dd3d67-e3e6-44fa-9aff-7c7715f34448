const express = require('express');
const cors = require('cors');
const app = express();
const port = 3000;

// 中间件
app.use(cors({
  origin: ['http://localhost:5174', 'http://localhost:5175'],
  credentials: true
}));
app.use(express.json());

// 内存存储验证码
const verificationCodes = new Map();

// 内测配置
const BETA_CONFIG = {
  BETA_MODE: process.env.BETA_MODE === 'true' || true,
  BETA_CODE: process.env.BETA_CODE || 'weilynCHENliuYU'
};

console.log('🔐 内测配置:', BETA_CONFIG);

// 根路由
app.get('/api/v1', (req, res) => {
  res.json({
    service: 'SynText Beta Test Server',
    version: '1.0.0',
    status: 'running',
    betaMode: BETA_CONFIG.BETA_MODE,
    endpoints: [
      'POST /api/v1/auth/send-code',
      'POST /api/v1/auth/verify-code'
    ]
  });
});

// 健康检查
app.get('/api/v1/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 发送验证码
app.post('/api/v1/auth/send-code', (req, res) => {
  const { email } = req.body;
  
  if (!email) {
    return res.status(400).json({ message: '请输入邮箱地址' });
  }
  
  // 生成6位验证码
  const code = Math.floor(100000 + Math.random() * 900000).toString();
  const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5分钟过期
  
  // 存储验证码
  verificationCodes.set(email, { code, expiresAt });
  
  console.log(`📧 验证码已生成 - 邮箱: ${email}, 验证码: ${code}`);
  
  res.json({ 
    message: '验证码已发送到您的邮箱',
    // 在测试环境中返回验证码
    ...(process.env.NODE_ENV !== 'production' && { code })
  });
});

// 验证码登录/注册
app.post('/api/v1/auth/verify-code', (req, res) => {
  const { email, code, betaCode } = req.body;
  
  console.log('🔍 验证请求:', { email, code, betaCode: betaCode ? '***' : 'undefined' });
  
  // 内测验证码验证
  if (BETA_CONFIG.BETA_MODE) {
    if (!betaCode || betaCode !== BETA_CONFIG.BETA_CODE) {
      console.log('❌ 内测验证码错误:', betaCode);
      return res.status(401).json({ 
        message: '内测验证码错误，请联系管理员获取正确的验证码' 
      });
    }
    console.log('✅ 内测验证码验证通过');
  }
  
  if (!email || !code) {
    return res.status(400).json({ message: '请输入邮箱和验证码' });
  }
  
  // 验证邮箱验证码
  const storedCode = verificationCodes.get(email);
  if (!storedCode) {
    return res.status(401).json({ message: '验证码不存在或已过期' });
  }
  
  if (storedCode.expiresAt < new Date()) {
    verificationCodes.delete(email);
    return res.status(401).json({ message: '验证码已过期' });
  }
  
  if (storedCode.code !== code) {
    return res.status(401).json({ message: '验证码错误' });
  }
  
  // 删除已使用的验证码
  verificationCodes.delete(email);
  
  // 模拟用户数据
  const user = {
    id: Date.now().toString(),
    email: email,
    role: email.includes('admin') ? 'admin' : 'user',
    createdAt: new Date().toISOString()
  };
  
  // 模拟JWT token
  const access_token = `mock_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  console.log('✅ 登录成功:', { email, role: user.role });
  
  res.json({
    access_token,
    user
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('❌ 服务器错误:', err);
  res.status(500).json({ message: '服务器内部错误' });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({ message: '接口不存在' });
});

// 启动服务器
app.listen(port, () => {
  console.log(`🚀 内测验证服务器启动成功!`);
  console.log(`📍 服务地址: http://localhost:${port}`);
  console.log(`🔐 内测模式: ${BETA_CONFIG.BETA_MODE ? '启用' : '禁用'}`);
  console.log(`🔑 内测验证码: ${BETA_CONFIG.BETA_CODE}`);
  console.log(`📋 API文档: http://localhost:${port}/api/v1`);
  console.log('');
  console.log('🧪 测试步骤:');
  console.log('1. 访问前端: http://localhost:5174');
  console.log('2. 输入内测验证码: weilynCHENliuYU');
  console.log('3. 输入任意邮箱地址');
  console.log('4. 点击发送验证码，查看控制台获取验证码');
  console.log('5. 输入验证码完成登录');
});
