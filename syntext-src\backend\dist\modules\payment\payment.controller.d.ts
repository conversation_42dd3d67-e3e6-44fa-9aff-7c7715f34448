import { PaymentService } from './payment.service';
import { CreatePaymentDto } from './dto/payment.dto';
export declare class PaymentController {
    private readonly paymentService;
    constructor(paymentService: PaymentService);
    getPlans(): Promise<{
        type: import("../subscription/entities/subscription.entity").PlanType;
        name: string;
        price: number;
        translationLimit: number;
        features: string[];
    }[]>;
    createPayment(req: any, createPaymentDto: CreatePaymentDto): Promise<{
        paymentId: string;
        orderNumber: string;
        paymentUrl: string;
        paymentForm: string;
        amount: number;
        currency: string;
        extraData: Record<string, any>;
    }>;
    handleCallback(method: string, callbackData: any): Promise<{
        success: boolean;
        message: string;
    }>;
    getUserOrders(req: any, page?: number, limit?: number): Promise<{
        payments: import("./entities/payment.entity").Payment[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getOrderDetail(req: any, id: string): Promise<import("./entities/payment.entity").Payment>;
    cancelOrder(req: any, id: string): Promise<{
        message: string;
    }>;
    validateCoupon(body: {
        code: string;
    }): Promise<{
        valid: boolean;
        coupon?: import("../coupon/entities/coupon.entity").Coupon;
        message?: string;
    }>;
    calculatePrice(body: {
        planType: string;
        couponCode?: string;
    }): Promise<import("./dto/payment.dto").PriceCalculationDto>;
    getAllOrders(page?: number, limit?: number, status?: string): Promise<{
        payments: import("./entities/payment.entity").Payment[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getPaymentStats(): Promise<import("./dto/payment.dto").PaymentStatsDto>;
    adminRefund(id: string, body: {
        reason: string;
        amount?: number;
    }): Promise<{
        message: string;
        refundOrderNumber: string;
        refundAmount: number;
    }>;
}
