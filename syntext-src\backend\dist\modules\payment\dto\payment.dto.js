"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentStatsDto = exports.PaymentOrderDto = exports.PriceCalculationDto = exports.PaymentCallbackDto = exports.CreatePaymentDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const payment_entity_1 = require("../entities/payment.entity");
const subscription_entity_1 = require("../../subscription/entities/subscription.entity");
class CreatePaymentDto {
}
exports.CreatePaymentDto = CreatePaymentDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订阅套餐类型', enum: subscription_entity_1.PlanType }),
    (0, class_validator_1.IsEnum)(subscription_entity_1.PlanType),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "planType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付方式', enum: payment_entity_1.PaymentMethod }),
    (0, class_validator_1.IsEnum)(payment_entity_1.PaymentMethod),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "paymentMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "couponCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '返回地址', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "returnUrl", void 0);
class PaymentCallbackDto {
}
exports.PaymentCallbackDto = PaymentCallbackDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '回调数据' }),
    __metadata("design:type", Object)
], PaymentCallbackDto.prototype, "callbackData", void 0);
class PriceCalculationDto {
}
exports.PriceCalculationDto = PriceCalculationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '原始价格' }),
    __metadata("design:type", Number)
], PriceCalculationDto.prototype, "originalPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣金额' }),
    __metadata("design:type", Number)
], PriceCalculationDto.prototype, "discountAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最终价格' }),
    __metadata("design:type", Number)
], PriceCalculationDto.prototype, "finalPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码信息', required: false }),
    __metadata("design:type", Object)
], PriceCalculationDto.prototype, "couponInfo", void 0);
class PaymentOrderDto {
}
exports.PaymentOrderDto = PaymentOrderDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单号' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "orderNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付方式' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "paymentMethod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付状态' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付金额' }),
    __metadata("design:type", Number)
], PaymentOrderDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '原始金额' }),
    __metadata("design:type", Number)
], PaymentOrderDto.prototype, "originalAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣金额' }),
    __metadata("design:type", Number)
], PaymentOrderDto.prototype, "discountAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '货币类型' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品描述' }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付链接', required: false }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "paymentUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '第三方订单ID', required: false }),
    __metadata("design:type", String)
], PaymentOrderDto.prototype, "thirdPartyOrderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], PaymentOrderDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付完成时间', required: false }),
    __metadata("design:type", Date)
], PaymentOrderDto.prototype, "paidAt", void 0);
class PaymentStatsDto {
}
exports.PaymentStatsDto = PaymentStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总订单数' }),
    __metadata("design:type", Number)
], PaymentStatsDto.prototype, "totalOrders", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '成功订单数' }),
    __metadata("design:type", Number)
], PaymentStatsDto.prototype, "successOrders", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '失败订单数' }),
    __metadata("design:type", Number)
], PaymentStatsDto.prototype, "failedOrders", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '待支付订单数' }),
    __metadata("design:type", Number)
], PaymentStatsDto.prototype, "pendingOrders", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总收入' }),
    __metadata("design:type", Number)
], PaymentStatsDto.prototype, "totalRevenue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '本月收入' }),
    __metadata("design:type", Number)
], PaymentStatsDto.prototype, "monthlyRevenue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '今日收入' }),
    __metadata("design:type", Number)
], PaymentStatsDto.prototype, "dailyRevenue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付方式统计' }),
    __metadata("design:type", Array)
], PaymentStatsDto.prototype, "paymentMethodStats", void 0);
//# sourceMappingURL=payment.dto.js.map