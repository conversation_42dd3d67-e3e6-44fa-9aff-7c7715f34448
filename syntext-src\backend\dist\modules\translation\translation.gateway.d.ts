import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { TranslationService } from './translation.service';
import { JwtService } from '@nestjs/jwt';
import { SpeechService } from '../speech/speech.service';
interface AuthenticatedSocket extends Socket {
    userId?: string;
    userEmail?: string;
}
export declare class TranslationGateway implements OnGatewayConnection, OnGatewayDisconnect {
    private readonly translationService;
    private readonly jwtService;
    private readonly speechService;
    server: Server;
    private readonly logger;
    constructor(translationService: TranslationService, jwtService: JwtService, speechService: SpeechService);
    handleConnection(client: AuthenticatedSocket): Promise<void>;
    handleDisconnect(client: AuthenticatedSocket): void;
    handleTranslate(data: {
        text: string;
        sourceLanguage: string;
        targetLanguage: string;
    }, client: AuthenticatedSocket): Promise<void>;
    handleVoiceStream(data: {
        audioData: string;
        language: string;
        sessionId?: string;
    }, client: AuthenticatedSocket): Promise<void>;
    handleGetHistory(data: {
        limit?: number;
    }, client: AuthenticatedSocket): Promise<void>;
}
export {};
