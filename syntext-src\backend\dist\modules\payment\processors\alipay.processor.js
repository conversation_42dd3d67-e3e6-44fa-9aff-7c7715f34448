"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var AlipayProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlipayProcessor = void 0;
const common_1 = require("@nestjs/common");
const payment_entity_1 = require("../entities/payment.entity");
let AlipayProcessor = AlipayProcessor_1 = class AlipayProcessor {
    constructor() {
        this.logger = new common_1.Logger(AlipayProcessor_1.name);
        this.paymentMethod = payment_entity_1.PaymentMethod.ALIPAY;
        this.config = {
            appId: process.env.ALIPAY_APP_ID || 'mock_app_id',
            privateKey: process.env.ALIPAY_PRIVATE_KEY || 'mock_private_key',
            publicKey: process.env.ALIPAY_PUBLIC_KEY || 'mock_public_key',
            gatewayUrl: process.env.ALIPAY_GATEWAY_URL || 'https://openapi.alipay.com/gateway.do',
            signType: 'RSA2',
            charset: 'utf-8',
            version: '1.0',
        };
    }
    async createPayment(params) {
        try {
            this.logger.log(`Creating Alipay payment for order: ${params.orderNumber}`);
            if (this.isProductionConfigured()) {
                return await this.createRealPayment(params);
            }
            else {
                return await this.createMockPayment(params);
            }
        }
        catch (error) {
            this.logger.error('Failed to create Alipay payment:', error);
            return {
                success: false,
                error: '创建支付宝支付失败',
            };
        }
    }
    async verifyCallback(callbackData) {
        try {
            this.logger.log('Verifying Alipay callback');
            if (this.isProductionConfigured()) {
                return await this.verifyRealCallback(callbackData);
            }
            else {
                return await this.verifyMockCallback(callbackData);
            }
        }
        catch (error) {
            this.logger.error('Failed to verify Alipay callback:', error);
            return {
                verified: false,
                error: '回调验证失败',
            };
        }
    }
    async queryPaymentStatus(thirdPartyOrderId) {
        try {
            this.logger.log(`Querying Alipay payment status: ${thirdPartyOrderId}`);
            if (this.isProductionConfigured()) {
                return await this.queryRealPaymentStatus(thirdPartyOrderId);
            }
            else {
                return await this.queryMockPaymentStatus(thirdPartyOrderId);
            }
        }
        catch (error) {
            this.logger.error('Failed to query Alipay payment status:', error);
            return {
                success: false,
                error: '查询支付状态失败',
            };
        }
    }
    async refund(params) {
        try {
            this.logger.log(`Processing Alipay refund: ${params.refundOrderNumber}`);
            if (this.isProductionConfigured()) {
                return await this.processRealRefund(params);
            }
            else {
                return await this.processMockRefund(params);
            }
        }
        catch (error) {
            this.logger.error('Failed to process Alipay refund:', error);
            return {
                success: false,
                error: '退款处理失败',
            };
        }
    }
    isProductionConfigured() {
        return (this.config.appId !== 'mock_app_id' &&
            this.config.privateKey !== 'mock_private_key' &&
            this.config.publicKey !== 'mock_public_key');
    }
    async createRealPayment(params) {
        this.logger.warn('Real Alipay payment not implemented yet');
        return {
            success: false,
            error: '支付宝支付功能正在开发中，敬请期待',
        };
    }
    async createMockPayment(params) {
        const thirdPartyOrderId = `alipay_mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const paymentUrl = `https://mock-alipay.com/pay?order_id=${thirdPartyOrderId}&amount=${params.amount}`;
        this.logger.log(`Mock Alipay payment created: ${thirdPartyOrderId}`);
        return {
            success: true,
            thirdPartyOrderId,
            paymentUrl,
            extraData: {
                mockPayment: true,
                message: '这是模拟支付，实际不会扣费',
            },
        };
    }
    async verifyRealCallback(callbackData) {
        this.logger.warn('Real Alipay callback verification not implemented yet');
        return {
            verified: false,
            error: '回调验证功能正在开发中',
        };
    }
    async verifyMockCallback(callbackData) {
        if (callbackData && callbackData.trade_status === 'TRADE_SUCCESS') {
            return {
                verified: true,
                thirdPartyOrderId: callbackData.out_trade_no,
                paymentStatus: 'success',
                amount: parseFloat(callbackData.total_amount) * 100,
                rawData: callbackData,
            };
        }
        return {
            verified: false,
            error: '模拟回调验证失败',
        };
    }
    async queryRealPaymentStatus(thirdPartyOrderId) {
        this.logger.warn('Real Alipay payment status query not implemented yet');
        return {
            success: false,
            error: '支付状态查询功能正在开发中',
        };
    }
    async queryMockPaymentStatus(thirdPartyOrderId) {
        if (thirdPartyOrderId.startsWith('alipay_mock_')) {
            const statuses = ['success', 'pending', 'failed'];
            const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
            return {
                success: true,
                status: randomStatus,
                amount: Math.floor(Math.random() * 10000) + 1000,
                paidAt: randomStatus === 'success' ? new Date() : undefined,
            };
        }
        return {
            success: false,
            error: '订单不存在',
        };
    }
    async processRealRefund(params) {
        this.logger.warn('Real Alipay refund not implemented yet');
        return {
            success: false,
            error: '退款功能正在开发中',
        };
    }
    async processMockRefund(params) {
        const thirdPartyRefundId = `alipay_refund_mock_${Date.now()}`;
        this.logger.log(`Mock Alipay refund processed: ${thirdPartyRefundId}`);
        return {
            success: true,
            thirdPartyRefundId,
        };
    }
};
exports.AlipayProcessor = AlipayProcessor;
exports.AlipayProcessor = AlipayProcessor = AlipayProcessor_1 = __decorate([
    (0, common_1.Injectable)()
], AlipayProcessor);
//# sourceMappingURL=alipay.processor.js.map