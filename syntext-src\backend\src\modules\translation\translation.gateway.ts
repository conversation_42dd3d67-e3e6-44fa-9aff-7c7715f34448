import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { TranslationService, TranslateRequest } from './translation.service';
import { JwtService } from '@nestjs/jwt';
import { SpeechService } from '../speech/speech.service';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userEmail?: string;
}

@WebSocketGateway({
  cors: {
    origin: [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:5175',
      'http://localhost:5176',
      'http://localhost:5177',
      'http://localhost:5178',
    ],
    credentials: true,
  },
  namespace: '/translation',
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000,
})
export class TranslationGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(TranslationGateway.name);

  constructor(
    private readonly translationService: TranslationService,
    private readonly jwtService: JwtService,
    private readonly speechService: SpeechService,
  ) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      const token = client.handshake.auth?.token ||
                   client.handshake.headers?.authorization?.replace('Bearer ', '') ||
                   client.handshake.query?.token;

      if (!token) {
        this.logger.warn(`Connection rejected: No token provided (${client.id}) from ${client.handshake.address}`);
        client.emit('error', {
          message: '认证失败：缺少token',
          code: 'NO_TOKEN',
          timestamp: new Date()
        });
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token);
      client.userId = payload.sub;
      client.userEmail = payload.email;

      this.logger.log(`Client connected: ${client.userEmail} (${client.id}) from ${client.handshake.address}`);

      // 发送连接成功消息
      client.emit('authenticated', {
        userId: client.userId,
        email: client.userEmail,
        timestamp: new Date(),
        serverTime: new Date().toISOString()
      });

      // 发送服务器状态
      client.emit('server_status', {
        speechRecognition: 'available',
        translation: 'available',
        aiQA: 'available',
        timestamp: new Date()
      });

    } catch (error) {
      this.logger.error(`Authentication failed for client ${client.id}:`, error);
      client.emit('error', {
        message: '认证失败：token无效或已过期',
        code: 'INVALID_TOKEN',
        timestamp: new Date()
      });
      client.disconnect();
    }
  }

  handleDisconnect(client: AuthenticatedSocket) {
    this.logger.log(`Client disconnected: ${client.userEmail} (${client.id})`);
  }

  @SubscribeMessage('translate')
  async handleTranslate(
    @MessageBody() data: { text: string; sourceLanguage: string; targetLanguage: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      if (!client.userId) {
        client.emit('error', { message: '用户未认证' });
        return;
      }

      this.logger.log(`Translation request from ${client.userEmail}: ${data.text}`);

      const request: TranslateRequest = {
        text: data.text,
        sourceLanguage: data.sourceLanguage,
        targetLanguage: data.targetLanguage,
        userId: client.userId,
      };

      const result = await this.translationService.translateText(request);

      // 发送翻译结果
      client.emit('translation_result', {
        id: Date.now().toString(),
        originalText: result.originalText,
        translatedText: result.translatedText,
        sourceLanguage: result.sourceLanguage,
        targetLanguage: result.targetLanguage,
        timestamp: new Date(),
        isQuestion: result.isQuestion,
      });

      // 如果检测到问题，发送AI回答
      if (result.isQuestion && result.aiAnswer) {
        client.emit('question_answer', {
          id: Date.now().toString(),
          question: result.originalText,
          answer: result.aiAnswer,
          timestamp: new Date(),
        });
      }

    } catch (error) {
      this.logger.error('Translation error:', error);
      client.emit('error', { message: error.message || '翻译失败' });
    }
  }

  @SubscribeMessage('voice_stream')
  async handleVoiceStream(
    @MessageBody() data: { audioData: string; language: string; sessionId?: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    const startTime = Date.now();
    const sessionId = data.sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      if (!client.userId) {
        client.emit('error', {
          message: '用户未认证',
          code: 'UNAUTHORIZED',
          sessionId,
          timestamp: new Date()
        });
        return;
      }

      // 验证输入数据
      if (!data.audioData || !data.language) {
        client.emit('error', {
          message: '缺少必要的音频数据或语言参数',
          code: 'INVALID_INPUT',
          sessionId,
          timestamp: new Date()
        });
        return;
      }

      this.logger.log(`Processing voice stream from ${client.userEmail}, language: ${data.language}, sessionId: ${sessionId}`);

      // 发送处理开始通知
      client.emit('voice_processing_start', {
        sessionId,
        language: data.language,
        timestamp: new Date()
      });

      // 将 Base64 音频数据转换为 Buffer
      let audioBuffer: Buffer;
      try {
        audioBuffer = Buffer.from(data.audioData, 'base64');

        // 验证音频数据大小
        if (audioBuffer.length === 0) {
          throw new Error('Empty audio data');
        }

        if (audioBuffer.length > 10 * 1024 * 1024) { // 10MB limit
          throw new Error('Audio data too large');
        }

      } catch (error) {
        this.logger.error('Failed to decode audio data:', error);
        client.emit('error', {
          message: '音频数据格式错误或过大',
          code: 'INVALID_AUDIO_DATA',
          sessionId,
          timestamp: new Date()
        });
        return;
      }

      // 使用语音识别服务
      const speechStartTime = Date.now();
      const speechResult = await this.speechService.speechToText(audioBuffer, data.language);
      const speechDuration = Date.now() - speechStartTime;

      if (!speechResult.text || speechResult.text.trim().length === 0) {
        this.logger.warn(`No speech detected in audio for session ${sessionId}`);
        client.emit('voice_processing_complete', {
          sessionId,
          success: false,
          message: '未检测到语音内容',
          processingTime: Date.now() - startTime,
          timestamp: new Date()
        });
        return;
      }

      this.logger.log(`Speech recognition result: "${speechResult.text}" (confidence: ${speechResult.confidence}, duration: ${speechDuration}ms)`);

      // 发送语音识别结果
      client.emit('speech_recognition_result', {
        text: speechResult.text,
        language: data.language,
        confidence: speechResult.confidence,
        sessionId,
        processingTime: speechDuration,
        timestamp: new Date(),
      });

      // 自动触发翻译
      const targetLanguage = data.language === 'zh-CN' ? 'en-US' : 'zh-CN';

      const request: TranslateRequest = {
        text: speechResult.text,
        sourceLanguage: data.language,
        targetLanguage: targetLanguage,
        userId: client.userId,
      };

      const translationStartTime = Date.now();
      const result = await this.translationService.translateText(request);
      const translationDuration = Date.now() - translationStartTime;

      client.emit('translation_result', {
        id: Date.now().toString(),
        originalText: result.originalText,
        translatedText: result.translatedText,
        sourceLanguage: result.sourceLanguage,
        targetLanguage: result.targetLanguage,
        timestamp: new Date(),
        isQuestion: result.isQuestion,
        confidence: speechResult.confidence,
        sessionId,
        processingTime: translationDuration,
      });

      // 如果检测到问题，生成AI回答
      if (result.isQuestion && result.aiAnswer) {
        client.emit('question_answer', {
          id: Date.now().toString(),
          question: result.originalText,
          answer: result.aiAnswer,
          sessionId,
          timestamp: new Date(),
        });
      }

      // 发送处理完成通知
      const totalDuration = Date.now() - startTime;
      client.emit('voice_processing_complete', {
        sessionId,
        success: true,
        totalProcessingTime: totalDuration,
        speechRecognitionTime: speechDuration,
        translationTime: translationDuration,
        timestamp: new Date()
      });

      this.logger.log(`Voice processing completed for session ${sessionId} in ${totalDuration}ms`);

    } catch (error) {
      const totalDuration = Date.now() - startTime;
      this.logger.error(`Voice stream error for session ${sessionId}:`, error);

      client.emit('error', {
        message: '语音处理失败: ' + (error.message || '未知错误'),
        code: 'VOICE_PROCESSING_ERROR',
        sessionId,
        processingTime: totalDuration,
        timestamp: new Date()
      });

      client.emit('voice_processing_complete', {
        sessionId,
        success: false,
        error: error.message,
        processingTime: totalDuration,
        timestamp: new Date()
      });
    }
  }



  @SubscribeMessage('get_translation_history')
  async handleGetHistory(
    @MessageBody() data: { limit?: number },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      if (!client.userId) {
        client.emit('error', { message: '用户未认证' });
        return;
      }

      const history = await this.translationService.findByUser(client.userId, data.limit || 20);
      client.emit('translation_history', history);

    } catch (error) {
      this.logger.error('Get history error:', error);
      client.emit('error', { message: '获取历史记录失败' });
    }
  }
}
