// 内测配置
export const BETA_CONFIG = {
  // 是否启用内测模式
  BETA_MODE: import.meta.env.VITE_BETA_MODE === 'true' || true, // 默认启用内测模式
  
  // 内测验证码
  BETA_CODE: import.meta.env.VITE_BETA_CODE || 'weilynCHENliuYU',
  
  // 内测提示信息
  BETA_MESSAGES: {
    TITLE: '内测阶段',
    DESCRIPTION: '需要输入内测验证码才能注册或登录',
    ADMIN_DESCRIPTION: '管理后台需要输入内测验证码才能登录',
    PLACEHOLDER: '请输入内测验证码',
    HELP_TEXT: '请联系管理员获取内测验证码',
    ERROR_MESSAGE: '内测验证码错误，请联系管理员获取正确的验证码'
  }
};

// 验证内测验证码
export const validateBetaCode = (inputCode: string): boolean => {
  if (!BETA_CONFIG.BETA_MODE) {
    return true; // 如果未启用内测模式，直接通过
  }
  
  return inputCode === BETA_CONFIG.BETA_CODE;
};

// 获取内测错误信息
export const getBetaErrorMessage = (): string => {
  return BETA_CONFIG.BETA_MESSAGES.ERROR_MESSAGE;
};
