import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Translation } from './entities/translation.entity';
export interface TranslateRequest {
    text: string;
    sourceLanguage: string;
    targetLanguage: string;
    userId: string;
}
export interface TranslateResponse {
    originalText: string;
    translatedText: string;
    sourceLanguage: string;
    targetLanguage: string;
    isQuestion: boolean;
    aiAnswer?: string;
}
export declare class TranslationService {
    private translationRepository;
    private configService;
    private readonly logger;
    private readonly deepseekApiKey;
    private readonly deepseekApiUrl;
    constructor(translationRepository: Repository<Translation>, configService: ConfigService);
    translateText(request: TranslateRequest): Promise<TranslateResponse>;
    private callDeepSeekTranslation;
    private detectQuestion;
    private aiDetectQuestion;
    private generateAnswer;
    private mockAnswer;
    private getLanguageName;
    create(translationData: Partial<Translation>): Promise<Translation>;
    findByUser(userId: string, limit?: number): Promise<Translation[]>;
    getTranslationStats(): Promise<{
        total: number;
        today: number;
        questions: number;
    }>;
}
