"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const payment_service_1 = require("./payment.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../auth/guards/roles.guard");
const roles_decorator_1 = require("../../auth/decorators/roles.decorator");
const user_entity_1 = require("../user/entities/user.entity");
const payment_dto_1 = require("./dto/payment.dto");
let PaymentController = class PaymentController {
    constructor(paymentService) {
        this.paymentService = paymentService;
    }
    getPlans() {
        return this.paymentService.getSubscriptionPlans();
    }
    createPayment(req, createPaymentDto) {
        return this.paymentService.createPayment(req.user.id, createPaymentDto);
    }
    async handleCallback(method, callbackData) {
        return this.paymentService.handlePaymentCallback(method, callbackData);
    }
    getUserOrders(req, page = 1, limit = 10) {
        return this.paymentService.getUserPayments(req.user.id, page, limit);
    }
    getOrderDetail(req, id) {
        return this.paymentService.getPaymentDetail(req.user.id, id);
    }
    cancelOrder(req, id) {
        return this.paymentService.cancelPayment(req.user.id, id);
    }
    validateCoupon(body) {
        return this.paymentService.validateCoupon(body.code);
    }
    calculatePrice(body) {
        return this.paymentService.calculatePrice(body.planType, body.couponCode);
    }
    getAllOrders(page = 1, limit = 10, status) {
        return this.paymentService.getAllPayments(page, limit, status);
    }
    getPaymentStats() {
        return this.paymentService.getPaymentStats();
    }
    adminRefund(id, body) {
        return this.paymentService.processRefund(id, body.reason, body.amount);
    }
};
exports.PaymentController = PaymentController;
__decorate([
    (0, common_1.Get)('plans'),
    (0, swagger_1.ApiOperation)({ summary: '获取订阅套餐' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], PaymentController.prototype, "getPlans", null);
__decorate([
    (0, common_1.Post)('create'),
    (0, swagger_1.ApiOperation)({ summary: '创建支付订单' }),
    (0, swagger_1.ApiBody)({ type: payment_dto_1.CreatePaymentDto }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, payment_dto_1.CreatePaymentDto]),
    __metadata("design:returntype", void 0)
], PaymentController.prototype, "createPayment", null);
__decorate([
    (0, common_1.Post)('callback/:method'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '支付回调处理' }),
    (0, swagger_1.ApiParam)({ name: 'method', description: '支付方式', enum: ['alipay', 'wechat', 'stripe'] }),
    __param(0, (0, common_1.Param)('method')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "handleCallback", null);
__decorate([
    (0, common_1.Get)('orders'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户支付订单' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: '页码' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: '每页数量' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", void 0)
], PaymentController.prototype, "getUserOrders", null);
__decorate([
    (0, common_1.Get)('orders/:id'),
    (0, swagger_1.ApiOperation)({ summary: '获取支付订单详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '订单ID' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], PaymentController.prototype, "getOrderDetail", null);
__decorate([
    (0, common_1.Post)('orders/:id/cancel'),
    (0, swagger_1.ApiOperation)({ summary: '取消支付订单' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '订单ID' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], PaymentController.prototype, "cancelOrder", null);
__decorate([
    (0, common_1.Post)('validate-coupon'),
    (0, swagger_1.ApiOperation)({ summary: '验证优惠码' }),
    (0, swagger_1.ApiBody)({ schema: { properties: { code: { type: 'string' } } } }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], PaymentController.prototype, "validateCoupon", null);
__decorate([
    (0, common_1.Post)('calculate-price'),
    (0, swagger_1.ApiOperation)({ summary: '计算优惠后价格' }),
    (0, swagger_1.ApiBody)({
        schema: {
            properties: {
                planType: { type: 'string' },
                couponCode: { type: 'string', required: false },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], PaymentController.prototype, "calculatePrice", null);
__decorate([
    (0, common_1.Get)('admin/orders'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: '管理员获取所有订单' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: '页码' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: '每页数量' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, description: '订单状态' }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String]),
    __metadata("design:returntype", void 0)
], PaymentController.prototype, "getAllOrders", null);
__decorate([
    (0, common_1.Get)('admin/stats'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: '获取支付统计' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], PaymentController.prototype, "getPaymentStats", null);
__decorate([
    (0, common_1.Post)('admin/refund/:id'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: '管理员退款' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '订单ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            properties: {
                reason: { type: 'string' },
                amount: { type: 'number', required: false },
            },
        },
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], PaymentController.prototype, "adminRefund", null);
exports.PaymentController = PaymentController = __decorate([
    (0, swagger_1.ApiTags)('支付'),
    (0, common_1.Controller)('payments'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [payment_service_1.PaymentService])
], PaymentController);
//# sourceMappingURL=payment.controller.js.map