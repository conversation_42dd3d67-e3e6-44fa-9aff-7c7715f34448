import { useState, useRef, useEffect } from 'react';

export interface VoiceRecordingHook {
  isRecording: boolean;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  audioLevel: number;
  error: string | null;
}

// 全局语音识别实例管理器
class SpeechRecognitionManager {
  private static instance: SpeechRecognitionManager;
  private currentRecognition: any = null;
  private isActive: boolean = false;

  static getInstance(): SpeechRecognitionManager {
    if (!SpeechRecognitionManager.instance) {
      SpeechRecognitionManager.instance = new SpeechRecognitionManager();
    }
    return SpeechRecognitionManager.instance;
  }

  async stopCurrent(): Promise<void> {
    if (this.currentRecognition && this.isActive) {
      console.log('🛑 停止当前全局语音识别实例');
      return new Promise((resolve) => {
        const cleanup = () => {
          this.currentRecognition = null;
          this.isActive = false;
          resolve();
        };

        this.currentRecognition.onend = cleanup;
        this.currentRecognition.onerror = cleanup;

        try {
          this.currentRecognition.abort();
        } catch (error) {
          console.log('停止识别时出错:', error);
          cleanup();
        }

        // 强制清理，防止卡住
        setTimeout(cleanup, 500);
      });
    }
  }

  setCurrent(recognition: any): void {
    this.currentRecognition = recognition;
    this.isActive = true;
  }

  getCurrent(): any {
    return this.currentRecognition;
  }

  isCurrentActive(): boolean {
    return this.isActive;
  }
}

const speechManager = SpeechRecognitionManager.getInstance();

export const useSpeechRecognition = (
  onSpeechResult: (text: string, confidence: number, isFinal: boolean) => void,
  onError?: (error: string) => void,
  language: string = 'zh-CN'
): VoiceRecordingHook => {
  // 所有 useState 调用必须在最前面，顺序固定
  const [isRecording, setIsRecording] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // 所有 useRef 调用必须在 useState 之后，顺序固定
  const recognitionRef = useRef<any>(null);
  const isActiveRef = useRef(false);
  const retryCountRef = useRef(0);
  const restartTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastRestartTimeRef = useRef(0);
  const wasAbortedRef = useRef(false);
  const onSpeechResultRef = useRef(onSpeechResult);
  const onErrorRef = useRef(onError);

  // 常量定义
  const MAX_RETRIES = 5;
  const MIN_RESTART_DELAY = 1000;
  const RESTART_COOLDOWN = 2000;

  // 更新回调引用
  onSpeechResultRef.current = onSpeechResult;
  onErrorRef.current = onError;

  // 安全重启语音识别 - 使用普通函数避免 hook 顺序问题
  const scheduleRestart = () => {
    // Clear any existing restart timeout
    if (restartTimeoutRef.current) {
      clearTimeout(restartTimeoutRef.current);
    }

    const delay = Math.max(MIN_RESTART_DELAY, RESTART_COOLDOWN - (Date.now() - lastRestartTimeRef.current));

    restartTimeoutRef.current = setTimeout(() => {
      if (isActiveRef.current && recognitionRef.current && !wasAbortedRef.current) {
        try {
          lastRestartTimeRef.current = Date.now();
          recognitionRef.current.start();
          console.log('🔄 安全重启语音识别');
        } catch (error: any) {
          console.error('重启语音识别失败:', error);

          // If already running, don't count as error
          if (error.message && error.message.includes('already started')) {
            console.log('⚠️ 识别已在运行，跳过重启');
            return;
          }

          retryCountRef.current++;
          if (retryCountRef.current < MAX_RETRIES) {
            scheduleRestart(); // Retry with exponential backoff
          } else {
            setError('语音识别多次失败，请刷新页面重试');
            isActiveRef.current = false;
          }
        }
      }
    }, delay);
  };

  // 检查浏览器支持 - 普通函数
  const checkBrowserSupport = () => {
    console.log('🔍 检查浏览器语音识别支持');

    // 检查基本支持
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (!SpeechRecognition) {
      throw new Error('浏览器不支持语音识别，请使用Chrome浏览器');
    }

    // 检查是否在安全上下文中
    if (!window.isSecureContext) {
      console.warn('⚠️ 不在安全上下文中，语音识别可能不稳定');
    }

    // 检查是否在用户激活状态下
    if (!document.hasFocus()) {
      console.warn('⚠️ 页面未获得焦点，可能影响语音识别');
    }

    console.log('✅ 浏览器支持检查通过');
    return SpeechRecognition;
  };

  // 初始化语音识别 - 普通函数
  const initializeSpeechRecognition = () => {
    const SpeechRecognition = checkBrowserSupport();
    const recognition = new SpeechRecognition();

    // 关键配置
    recognition.continuous = true;      // 连续识别
    recognition.interimResults = true;  // 中间结果
    recognition.lang = language;        // 动态语言
    recognition.maxAlternatives = 1;    // 最佳结果

    return recognition;
  };

  const startRecording = async () => {
    try {
      console.log('🚀 开始启动语音识别...');
      setError(null);

      // 使用全局管理器停止任何现有的识别实例
      console.log('🧹 停止所有现有语音识别实例');
      await speechManager.stopCurrent();

      // Prevent starting if already recording
      if (isActiveRef.current && recognitionRef.current) {
        console.log('⚠️ 语音识别已在运行中');
        return;
      }

      // Clear any pending restart timeouts
      if (restartTimeoutRef.current) {
        clearTimeout(restartTimeoutRef.current);
        restartTimeoutRef.current = null;
      }

      // 重置所有状态
      isActiveRef.current = false;
      retryCountRef.current = 0;
      wasAbortedRef.current = false;
      setIsRecording(false);

      // 等待确保清理完成
      await new Promise(resolve => setTimeout(resolve, 200));

      // 检查用户激活状态
      if (!document.hasFocus()) {
        console.log('⚠️ 页面未获得焦点，尝试获取焦点');
        window.focus();
      }

      // 初始化语音识别
      console.log('🔧 初始化新的语音识别实例');
      const recognition = initializeSpeechRecognition();

      // 注册到全局管理器
      speechManager.setCurrent(recognition);
      recognitionRef.current = recognition;
      isActiveRef.current = true;

      console.log('⚙️ 设置语音识别事件处理器');

      // 开始事件处理 - 必须在其他事件之前设置
      recognition.onstart = () => {
        console.log('🎤 语音识别开始');
        setIsRecording(true);
        setError(null);
        retryCountRef.current = 0;
        wasAbortedRef.current = false;
      };

      // 语音识别结果处理
      recognition.onresult = (event: any) => {
        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          const transcript = result[0].transcript?.trim();
          const confidence = result[0].confidence || 0.9;

          // Skip empty or very short results
          if (!transcript || transcript.length < 1) {
            continue;
          }

          if (result.isFinal) {
            // 最终结果：完整翻译
            console.log('🎯 最终结果:', transcript);
            onSpeechResultRef.current(transcript, confidence, true);
            setAudioLevel(0.8); // 模拟音量指示
          } else {
            // 中间结果：更激进的实时翻译策略
            const wordCount = transcript.split(/\s+/).length;
            const charCount = transcript.length;

            // 英文：2个词以上就翻译
            // 中文：3个字符以上就翻译
            const shouldTranslate = language === 'en-US' ? wordCount >= 2 : charCount >= 3;

            if (shouldTranslate) {
              console.log('⚡ 实时翻译:', transcript, `(${language === 'en-US' ? wordCount + '词' : charCount + '字'})`);
              onSpeechResultRef.current(transcript, confidence, false);
              setAudioLevel(0.6); // 模拟音量指示
            }
          }
        }
      };

      // 错误处理
      recognition.onerror = (event: any) => {
        console.error('❌ 语音识别错误:', event.error);
        console.log('🔍 错误详情:', {
          error: event.error,
          timeStamp: event.timeStamp,
          type: event.type,
          isActive: isActiveRef.current,
          recognitionState: recognitionRef.current ? 'exists' : 'null'
        });

        setIsRecording(false);
        setAudioLevel(0);

        // Handle different error types
        if (event.error === 'aborted') {
          console.log('🚫 语音识别被中止 - 分析原因:');
          console.log('  - 是否主动停止:', !isActiveRef.current);
          console.log('  - 识别实例状态:', recognitionRef.current ? '存在' : '已清空');
          console.log('  - 重试次数:', retryCountRef.current);

          // 如果是在启动后立即中止，可能是浏览器限制
          if (isActiveRef.current) {
            console.log('⚠️ 检测到启动后立即中止，可能原因:');
            console.log('  1. 浏览器安全策略限制');
            console.log('  2. 多个识别实例冲突');
            console.log('  3. 麦克风被其他应用占用');
            setError('语音识别启动失败，请检查麦克风权限或刷新页面重试');
          } else {
            setError('语音识别被中止');
          }

          wasAbortedRef.current = true; // Mark as aborted to prevent restart
          return;
        }

        if (event.error === 'not-allowed') {
          setError('请允许麦克风权限');
          isActiveRef.current = false;
          onErrorRef.current?.('请允许麦克风权限');
          return;
        }

        if (event.error === 'no-speech') {
          console.log('🔇 未检测到语音，继续监听');
          // Don't show error for no-speech, just continue
          return;
        }

        if (event.error === 'network') {
          setError('网络错误，请检查网络连接');
          onErrorRef.current?.('网络错误，请检查网络连接');
          return;
        }

        const errorMessage = `语音识别错误: ${event.error}`;
        setError(errorMessage);
        onErrorRef.current?.(errorMessage);

        // Auto-restart for recoverable errors
        if (isActiveRef.current && retryCountRef.current < MAX_RETRIES) {
          retryCountRef.current++;
          console.log(`🔄 错误后重试 (${retryCountRef.current}/${MAX_RETRIES})`);
          scheduleRestart();
        } else if (retryCountRef.current >= MAX_RETRIES) {
          console.log('❌ 达到最大重试次数，停止重试');
          setError('语音识别多次失败，请刷新页面重试');
          isActiveRef.current = false;
        }
      };

      // 自动重启机制
      recognition.onend = () => {
        console.log('🛑 语音识别结束');
        setIsRecording(false);
        setAudioLevel(0);

        // Don't restart if recognition was aborted
        if (wasAbortedRef.current) {
          console.log('🚫 跳过重启：识别被中止');
          wasAbortedRef.current = false; // Reset flag
          return;
        }

        // Only auto-restart if still active and not manually stopped
        if (isActiveRef.current && retryCountRef.current < MAX_RETRIES) {
          const now = Date.now();
          const timeSinceLastRestart = now - lastRestartTimeRef.current;

          if (timeSinceLastRestart > RESTART_COOLDOWN) {
            console.log('🔄 自动重启语音识别');
            scheduleRestart();
          } else {
            console.log('⏳ 重启冷却中，跳过自动重启');
          }
        }
      };

      // 确保在用户交互上下文中启动
      console.log('🚀 启动语音识别引擎...');

      // 添加一个小延迟，确保所有事件处理器都已设置
      await new Promise(resolve => setTimeout(resolve, 50));

      try {
        recognition.start();
        console.log('✅ 语音识别启动命令已发送');
      } catch (startError) {
        console.error('❌ 启动语音识别失败:', startError);
        throw new Error(`语音识别启动失败: ${startError.message}`);
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '语音识别启动失败';
      setError(errorMessage);
      onError?.(errorMessage);
      console.error('语音识别启动失败:', err);
    }
  };

  const stopRecording = async () => {
    try {
      console.log('🛑 开始停止语音识别');
      isActiveRef.current = false;

      // Clear any pending restart timeouts
      if (restartTimeoutRef.current) {
        clearTimeout(restartTimeoutRef.current);
        restartTimeoutRef.current = null;
      }

      // Reset retry count and abort flag
      retryCountRef.current = 0;
      wasAbortedRef.current = false;

      // 使用全局管理器停止识别
      await speechManager.stopCurrent();

      // 清理本地引用
      recognitionRef.current = null;

      setIsRecording(false);
      setAudioLevel(0);
      setError(null);

      console.log('✅ 语音识别已停止');

    } catch (err) {
      console.error('停止语音识别失败:', err);
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clean up on component unmount
      isActiveRef.current = false;

      if (restartTimeoutRef.current) {
        clearTimeout(restartTimeoutRef.current);
      }

      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
        } catch (error) {
          console.log('Cleanup: Recognition already stopped');
        }
      }
    };
  }, []);

  return {
    isRecording,
    startRecording,
    stopRecording,
    audioLevel,
    error
  };
};
