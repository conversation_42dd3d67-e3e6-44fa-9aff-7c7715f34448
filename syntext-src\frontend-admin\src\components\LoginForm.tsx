import React, { useState } from 'react';
import { Mail, Lock, Eye, EyeOff, Headphones, Shield, CheckCircle, AlertCircle } from 'lucide-react';
import axios from 'axios';
import { BETA_CONFIG, validateBetaCode, getBetaErrorMessage } from '../config/beta.config';

interface LoginFormProps {
  onLogin: (user: any) => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ onLogin }) => {
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [betaCode, setBetaCode] = useState(''); // 内测验证码
  const [isLoading, setIsLoading] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [error, setError] = useState('');



  const API_BASE_URL = 'http://localhost:3000/api/v1';

  const sendVerificationCode = async () => {
    if (!email) {
      setError('请输入邮箱地址');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      await axios.post(`${API_BASE_URL}/auth/send-code`, { email });
      setCodeSent(true);
      setCountdown(60);
      
      // 倒计时
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

    } catch (error: any) {
      setError(error.response?.data?.message || '发送验证码失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // 验证内测验证码
    if (BETA_CONFIG.BETA_MODE && !validateBetaCode(betaCode)) {
      setError(getBetaErrorMessage());
      return;
    }

    if (!email || !code) {
      setError('请输入邮箱和验证码');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await axios.post(`${API_BASE_URL}/auth/verify-code`, {
        email,
        code,
        betaCode // 发送内测验证码到后端
      });

      const { access_token, user } = response.data;
      
      // 检查用户权限
      if (user.role !== 'admin') {
        setError('您没有管理后台访问权限');
        return;
      }

      // 保存token
      localStorage.setItem('admin_token', access_token);
      localStorage.setItem('admin_user', JSON.stringify(user));
      
      onLogin(user);

    } catch (error: any) {
      setError(error.response?.data?.message || '登录失败');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Logo */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-3 mb-6">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-700 rounded-xl flex items-center justify-center shadow-lg">
                <Headphones className="w-7 h-7 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                <Shield className="w-2.5 h-2.5 text-white" />
              </div>
            </div>
            <div className="text-left">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-700 bg-clip-text text-transparent">SynText</span>
              <p className="text-sm text-gray-600 font-medium">管理后台</p>
            </div>
          </div>
          <h1 className="text-xl font-semibold text-gray-800 mb-2">欢迎回来</h1>
          <p className="text-sm text-gray-500">请使用管理员邮箱登录系统</p>
        </div>

        {/* 登录表单 */}
        <div className="bg-white rounded-xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm">
          {/* 内测提示 */}
          {BETA_CONFIG.BETA_MODE && (
            <div className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-amber-800">{BETA_CONFIG.BETA_MESSAGES.TITLE}</span>
              </div>
              <p className="text-xs text-amber-700 mt-1">
                {BETA_CONFIG.BETA_MESSAGES.ADMIN_DESCRIPTION}
              </p>
            </div>
          )}

          <form onSubmit={handleLogin} className="space-y-6">
            {/* 内测验证码输入 */}
            {BETA_CONFIG.BETA_MODE && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <span className="flex items-center space-x-2">
                    <span>内测验证码</span>
                    <span className="text-red-500">*</span>
                  </span>
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-amber-500" />
                  <input
                    type="text"
                    value={betaCode}
                    onChange={(e) => setBetaCode(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-amber-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all duration-200 bg-amber-50 focus:bg-white"
                    placeholder={BETA_CONFIG.BETA_MESSAGES.PLACEHOLDER}
                    required
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {BETA_CONFIG.BETA_MESSAGES.HELP_TEXT}
                </p>
              </div>
            )}
            {/* 邮箱输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                管理员邮箱
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
                  placeholder="请输入管理员邮箱"
                  required
                />
              </div>
            </div>

            {/* 验证码输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                邮箱验证码
              </label>
              <div className="flex space-x-3">
                <div className="relative flex-1">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={code}
                    onChange={(e) => setCode(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
                    placeholder="请输入6位验证码"
                    maxLength={6}
                    required
                  />
                </div>
                <button
                  type="button"
                  onClick={sendVerificationCode}
                  disabled={isLoading || countdown > 0}
                  className={`px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                    countdown > 0
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 shadow-md hover:shadow-lg'
                  }`}
                >
                  {countdown > 0 ? `${countdown}s` : codeSent ? '重新发送' : '发送验证码'}
                </button>
              </div>
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-3">
                <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            {/* 成功提示 */}
            {codeSent && !error && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                <p className="text-green-600 text-sm">验证码已发送到您的邮箱，请查收</p>
              </div>
            )}

            {/* 登录按钮 */}
            <button
              type="submit"
              disabled={isLoading}
              className={`w-full py-3 rounded-lg font-medium transition-all duration-200 ${
                isLoading
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-blue-600 to-purple-700 text-white hover:from-blue-700 hover:to-purple-800 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
              }`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>登录中...</span>
                </div>
              ) : (
                '登录管理后台'
              )}
            </button>
          </form>

          {/* 提示信息 */}
          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500">
              仅限管理员账户登录
            </p>
            <p className="text-xs text-gray-400 mt-2">
              测试账户：<EMAIL>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
