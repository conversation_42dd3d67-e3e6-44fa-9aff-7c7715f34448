import { DiscountType, CouponStatus } from '../entities/coupon.entity';
export declare class CreateCouponDto {
    code: string;
    name: string;
    description?: string;
    discountType: DiscountType;
    discountValue: number;
    usageLimit?: number;
    minOrderAmount?: number;
    expiresAt?: Date;
    status?: CouponStatus;
}
export declare class UpdateCouponDto {
    code?: string;
    name?: string;
    description?: string;
    discountType?: DiscountType;
    discountValue?: number;
    usageLimit?: number;
    minOrderAmount?: number;
    expiresAt?: Date;
    status?: CouponStatus;
}
export declare class BatchCreateCouponDto {
    count: number;
    name: string;
    description?: string;
    discountType: DiscountType;
    discountValue: number;
    usageLimit?: number;
    minOrderAmount?: number;
    expiresAt?: Date;
}
export declare class GenerateRandomCouponsDto {
    count: number;
    discountType: DiscountType;
    discountValue: number;
    name?: string;
    description?: string;
    usageLimit?: number;
    minOrderAmount?: number;
    expiresAt?: Date;
}
export declare class CouponStatsDto {
    total: number;
    active: number;
    inactive: number;
    expired: number;
    totalUsed: number;
    avgUsed: number;
    discountTypeStats: Array<{
        type: DiscountType;
        count: number;
    }>;
}
