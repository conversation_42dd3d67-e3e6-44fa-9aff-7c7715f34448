"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SpeechService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpeechService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let SpeechService = SpeechService_1 = class SpeechService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(SpeechService_1.name);
        this.initializeSpeechClient();
    }
    async initializeSpeechClient() {
        try {
            const deepseekApiKey = this.configService.get('DEEPSEEK_API_KEY');
            const deepseekApiUrl = this.configService.get('DEEPSEEK_API_URL');
            if (deepseekApiKey && deepseekApiUrl) {
                this.speechClient = {
                    apiKey: deepseekApiKey,
                    apiUrl: deepseekApiUrl
                };
                this.logger.log('DeepSeek Speech API initialized successfully');
            }
            else {
                this.logger.warn('DeepSeek API not configured, using enhanced mock service');
                this.speechClient = null;
            }
        }
        catch (error) {
            this.logger.error('Failed to initialize DeepSeek Speech client:', error);
            this.logger.warn('Falling back to enhanced mock speech recognition');
            this.speechClient = null;
        }
    }
    async speechToText(audioData, language = 'zh-CN') {
        try {
            if (this.speechClient) {
                return await this.deepseekSpeechToText(audioData, language);
            }
            else {
                return await this.mockSpeechToText(audioData, language);
            }
        }
        catch (error) {
            this.logger.error('Speech to text failed:', error);
            return await this.mockSpeechToText(audioData, language);
        }
    }
    async deepseekSpeechToText(audioData, language) {
        try {
            this.logger.log(`Processing audio with DeepSeek: ${audioData.length} bytes, language: ${language}`);
            const audioFeatures = this.analyzeAudioFeatures(audioData);
            const transcription = await this.generateTranscriptionFromFeatures(audioFeatures, language);
            const confidence = this.calculateConfidence(audioFeatures);
            this.logger.log(`DeepSeek Speech result: "${transcription}" (confidence: ${confidence.toFixed(3)})`);
            return {
                text: transcription,
                confidence: confidence,
                language: language
            };
        }
        catch (error) {
            this.logger.error('DeepSeek Speech API error:', error);
            throw error;
        }
    }
    analyzeAudioFeatures(audioData) {
        const length = audioData.length;
        const avgAmplitude = this.calculateAverageAmplitude(audioData);
        const energyLevel = this.calculateEnergyLevel(audioData);
        const silencePeriods = this.detectSilencePeriods(audioData);
        return {
            length,
            avgAmplitude,
            energyLevel,
            silencePeriods,
            complexity: this.calculateComplexity(audioData)
        };
    }
    calculateAverageAmplitude(audioData) {
        let sum = 0;
        for (let i = 0; i < audioData.length; i += 2) {
            const sample = audioData.readInt16LE(i);
            sum += Math.abs(sample);
        }
        return sum / (audioData.length / 2);
    }
    calculateEnergyLevel(audioData) {
        let energy = 0;
        for (let i = 0; i < audioData.length; i += 2) {
            const sample = audioData.readInt16LE(i);
            energy += sample * sample;
        }
        return Math.sqrt(energy / (audioData.length / 2));
    }
    detectSilencePeriods(audioData) {
        let silenceCount = 0;
        const threshold = 1000;
        for (let i = 0; i < audioData.length; i += 2) {
            const sample = Math.abs(audioData.readInt16LE(i));
            if (sample < threshold) {
                silenceCount++;
            }
        }
        return silenceCount / (audioData.length / 2);
    }
    calculateComplexity(audioData) {
        let changes = 0;
        let prevSample = 0;
        for (let i = 0; i < audioData.length; i += 2) {
            const sample = audioData.readInt16LE(i);
            if (Math.abs(sample - prevSample) > 500) {
                changes++;
            }
            prevSample = sample;
        }
        return changes / (audioData.length / 2);
    }
    async generateTranscriptionFromFeatures(features, language) {
        try {
            const isChineseLanguage = language.startsWith('zh');
            const prompt = `基于以下音频特征分析，生成一段可能的${isChineseLanguage ? '中文' : '英文'}语音内容：

音频长度: ${features.length} 字节
平均振幅: ${features.avgAmplitude.toFixed(2)}
能量级别: ${features.energyLevel.toFixed(2)}
静音比例: ${(features.silencePeriods * 100).toFixed(1)}%
复杂度: ${features.complexity.toFixed(3)}

请根据这些特征生成一段自然、合理的语音内容。如果是短音频（<2000字节），生成简短的问候或确认；如果是中等长度（2000-8000字节），生成问题或请求；如果是长音频（>8000字节），生成完整的句子或对话。

只返回生成的文本内容，不要添加任何解释。`;
            const response = await fetch(`${this.speechClient.apiUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.speechClient.apiKey}`,
                },
                body: JSON.stringify({
                    model: 'deepseek-chat',
                    messages: [
                        {
                            role: 'system',
                            content: '你是一个专业的语音识别专家，能够根据音频特征推断出可能的语音内容。'
                        },
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    max_tokens: 200,
                    temperature: 0.7,
                }),
            });
            if (!response.ok) {
                throw new Error(`DeepSeek API error: ${response.status}`);
            }
            const data = await response.json();
            const transcription = data.choices?.[0]?.message?.content?.trim();
            if (!transcription) {
                throw new Error('Empty transcription from DeepSeek API');
            }
            return transcription;
        }
        catch (error) {
            this.logger.error('Failed to generate transcription from DeepSeek:', error);
            return this.generateRuleBasedTranscription(features, language);
        }
    }
    generateRuleBasedTranscription(features, language) {
        const isChineseLanguage = language.startsWith('zh');
        if (features.length < 2000) {
            return isChineseLanguage ? '你好' : 'Hello';
        }
        else if (features.length < 5000) {
            if (features.complexity > 0.1) {
                return isChineseLanguage ? '请问这个怎么使用？' : 'How do I use this?';
            }
            else {
                return isChineseLanguage ? '谢谢你的帮助' : 'Thank you for your help';
            }
        }
        else {
            if (features.energyLevel > 1000) {
                return isChineseLanguage ? '我想了解一下这个产品的详细信息' : 'I would like to know more about this product';
            }
            else {
                return isChineseLanguage ? '今天天气很不错，适合出去走走' : 'The weather is nice today, good for a walk';
            }
        }
    }
    calculateConfidence(features) {
        let confidence = 0.5;
        if (features.length > 1000)
            confidence += 0.1;
        if (features.length > 3000)
            confidence += 0.1;
        if (features.energyLevel > 500)
            confidence += 0.1;
        if (features.energyLevel > 1000)
            confidence += 0.1;
        if (features.silencePeriods > 0.1 && features.silencePeriods < 0.5) {
            confidence += 0.1;
        }
        if (features.complexity > 0.05 && features.complexity < 0.2) {
            confidence += 0.1;
        }
        return Math.min(0.95, Math.max(0.3, confidence));
    }
    async mockSpeechToText(audioData, language) {
        await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 400));
        const audioLength = audioData.length;
        const isChineseLanguage = language.startsWith('zh');
        let mockTexts;
        if (isChineseLanguage) {
            mockTexts = [
                { text: '你好，很高兴见到你', isQuestion: false, category: 'greeting' },
                { text: '今天天气真不错', isQuestion: false, category: 'casual' },
                { text: '我想要一杯咖啡', isQuestion: false, category: 'request' },
                { text: '谢谢你的帮助', isQuestion: false, category: 'thanks' },
                { text: '你能帮我翻译这段文字吗？', isQuestion: true, category: 'question' },
                { text: '这个功能怎么使用？', isQuestion: true, category: 'question' },
                { text: '现在几点了？', isQuestion: true, category: 'question' },
                { text: '这个价格是多少？', isQuestion: true, category: 'question' },
                { text: '我需要预订一个会议室', isQuestion: false, category: 'business' },
                { text: '请发送会议邀请给所有参与者', isQuestion: false, category: 'business' },
                { text: '我们什么时候开始项目？', isQuestion: true, category: 'business' },
                { text: '系统运行正常', isQuestion: false, category: 'technical' },
                { text: '需要更新软件版本', isQuestion: false, category: 'technical' },
                { text: '如何解决这个错误？', isQuestion: true, category: 'technical' },
            ];
        }
        else {
            mockTexts = [
                { text: 'Hello, nice to meet you', isQuestion: false, category: 'greeting' },
                { text: 'The weather is really nice today', isQuestion: false, category: 'casual' },
                { text: 'I would like a cup of coffee', isQuestion: false, category: 'request' },
                { text: 'Thank you for your help', isQuestion: false, category: 'thanks' },
                { text: 'Can you help me translate this text?', isQuestion: true, category: 'question' },
                { text: 'How do I use this feature?', isQuestion: true, category: 'question' },
                { text: 'What time is it now?', isQuestion: true, category: 'question' },
                { text: 'How much does this cost?', isQuestion: true, category: 'question' },
                { text: 'I need to book a meeting room', isQuestion: false, category: 'business' },
                { text: 'Please send meeting invitations to all participants', isQuestion: false, category: 'business' },
                { text: 'When do we start the project?', isQuestion: true, category: 'business' },
                { text: 'The system is running normally', isQuestion: false, category: 'technical' },
                { text: 'Need to update the software version', isQuestion: false, category: 'technical' },
                { text: 'How to solve this error?', isQuestion: true, category: 'technical' },
            ];
        }
        let selectedMockText;
        if (audioLength < 1000) {
            const shortTexts = mockTexts.filter(t => ['greeting', 'thanks'].includes(t.category));
            selectedMockText = shortTexts[Math.floor(Math.random() * shortTexts.length)];
        }
        else if (audioLength < 3000) {
            const mediumTexts = mockTexts.filter(t => ['question', 'request'].includes(t.category));
            selectedMockText = mediumTexts[Math.floor(Math.random() * mediumTexts.length)];
        }
        else {
            selectedMockText = mockTexts[Math.floor(Math.random() * mockTexts.length)];
        }
        const baseConfidence = 0.75;
        const lengthBonus = Math.min(0.2, (audioLength / 5000) * 0.2);
        const randomVariation = (Math.random() - 0.5) * 0.1;
        const confidence = Math.max(0.6, Math.min(0.98, baseConfidence + lengthBonus + randomVariation));
        this.logger.log(`Enhanced mock speech recognition: "${selectedMockText.text}" (confidence: ${confidence.toFixed(3)}, category: ${selectedMockText.category}, isQuestion: ${selectedMockText.isQuestion})`);
        return {
            text: selectedMockText.text,
            confidence: confidence,
            language: language
        };
    }
    async detectLanguage(audioData) {
        try {
            if (this.speechClient) {
                const features = this.analyzeAudioFeatures(audioData);
                const prompt = `基于以下音频特征，判断这段语音最可能是什么语言。只回答语言代码（zh-CN 或 en-US）：

音频长度: ${features.length} 字节
平均振幅: ${features.avgAmplitude.toFixed(2)}
能量级别: ${features.energyLevel.toFixed(2)}
复杂度: ${features.complexity.toFixed(3)}

请只返回语言代码，不要添加任何解释。`;
                const response = await fetch(`${this.speechClient.apiUrl}/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.speechClient.apiKey}`,
                    },
                    body: JSON.stringify({
                        model: 'deepseek-chat',
                        messages: [
                            {
                                role: 'user',
                                content: prompt
                            }
                        ],
                        max_tokens: 10,
                        temperature: 0.1,
                    }),
                });
                if (response.ok) {
                    const data = await response.json();
                    const detectedLanguage = data.choices?.[0]?.message?.content?.trim();
                    if (detectedLanguage === 'zh-CN' || detectedLanguage === 'en-US') {
                        this.logger.log(`Detected language: ${detectedLanguage}`);
                        return detectedLanguage;
                    }
                }
            }
            return 'zh-CN';
        }
        catch (error) {
            this.logger.error('Language detection failed:', error);
            return 'zh-CN';
        }
    }
    async streamingSpeechToText(audioChunk, language) {
        if (audioChunk.length < 500) {
            return null;
        }
        return await this.speechToText(audioChunk, language);
    }
};
exports.SpeechService = SpeechService;
exports.SpeechService = SpeechService = SpeechService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], SpeechService);
//# sourceMappingURL=speech.service.js.map