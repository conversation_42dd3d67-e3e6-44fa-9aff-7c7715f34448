<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Speech Recognition Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        
        .recording { background: #dc3545 !important; animation: pulse 1s infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.7; } }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        
        .transcript {
            background: #e7f3ff;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            font-size: 18px;
            min-height: 60px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Speech Recognition Fix Test</h1>
        <p>This test verifies that the Web Speech API error loop has been fixed.</p>
        
        <div id="status" class="status info">Ready to test</div>
        
        <div>
            <button id="startBtn" onclick="startTest()">Start Speech Recognition</button>
            <button id="stopBtn" onclick="stopTest()" disabled>Stop Recognition</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="transcript">
            <strong>Transcript:</strong>
            <div id="transcript">Speak something to see the transcript here...</div>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let recognition = null;
        let isActive = false;
        let retryCount = 0;
        let lastRestartTime = 0;
        let restartTimeout = null;
        let wasAborted = false;
        
        const MAX_RETRIES = 5;
        const MIN_RESTART_DELAY = 1000;
        const RESTART_COOLDOWN = 2000;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666">[${timestamp}]</span> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function scheduleRestart() {
            if (restartTimeout) {
                clearTimeout(restartTimeout);
            }
            
            const delay = Math.max(MIN_RESTART_DELAY, RESTART_COOLDOWN - (Date.now() - lastRestartTime));
            
            restartTimeout = setTimeout(() => {
                if (isActive && recognition) {
                    try {
                        lastRestartTime = Date.now();
                        recognition.start();
                        log('🔄 Safe restart successful', 'success');
                    } catch (error) {
                        log(`❌ Restart failed: ${error.message}`, 'error');
                        retryCount++;
                        if (retryCount < MAX_RETRIES) {
                            scheduleRestart();
                        } else {
                            updateStatus('Max retries reached - stopping', 'error');
                            stopTest();
                        }
                    }
                }
            }, delay);
            
            log(`⏳ Restart scheduled in ${delay}ms`, 'warning');
        }
        
        function startTest() {
            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                updateStatus('Browser does not support speech recognition', 'error');
                log('❌ Browser not supported', 'error');
                return;
            }
            
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            recognition = new SpeechRecognition();
            
            recognition.continuous = true;
            recognition.interimResults = true;
            recognition.lang = 'en-US';
            
            recognition.onstart = () => {
                log('🎤 Speech recognition started', 'success');
                updateStatus('Listening... Speak now!', 'success');
                document.getElementById('startBtn').disabled = true;
                document.getElementById('startBtn').classList.add('recording');
                document.getElementById('stopBtn').disabled = false;
                retryCount = 0;
                wasAborted = false; // Reset abort flag on successful start
            };
            
            recognition.onresult = (event) => {
                let transcript = '';
                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const result = event.results[i];
                    transcript += result[0].transcript;
                    
                    if (result.isFinal) {
                        log(`🎯 Final: "${result[0].transcript}"`, 'success');
                    } else {
                        log(`⚡ Interim: "${result[0].transcript}"`, 'info');
                    }
                }
                document.getElementById('transcript').textContent = transcript || 'Speak something...';
            };
            
            recognition.onerror = (event) => {
                log(`❌ Error: ${event.error}`, 'error');

                if (event.error === 'aborted') {
                    log('🚫 Recognition aborted - marking to prevent restart', 'warning');
                    wasAborted = true;
                    return;
                }
                
                if (event.error === 'not-allowed') {
                    updateStatus('Microphone permission denied', 'error');
                    stopTest();
                    return;
                }
                
                if (event.error === 'no-speech') {
                    log('🔇 No speech detected - continuing', 'info');
                    return;
                }
                
                updateStatus(`Error: ${event.error}`, 'error');
                
                if (isActive && retryCount < MAX_RETRIES) {
                    retryCount++;
                    log(`🔄 Retry ${retryCount}/${MAX_RETRIES}`, 'warning');
                    scheduleRestart();
                } else if (retryCount >= MAX_RETRIES) {
                    log('❌ Max retries reached', 'error');
                    updateStatus('Too many errors - stopped', 'error');
                    stopTest();
                }
            };
            
            recognition.onend = () => {
                log('🛑 Recognition ended', 'info');

                // Don't restart if recognition was aborted
                if (wasAborted) {
                    log('🚫 Skipping restart: recognition was aborted', 'warning');
                    wasAborted = false; // Reset flag
                    return;
                }

                if (isActive && retryCount < MAX_RETRIES) {
                    const now = Date.now();
                    const timeSinceLastRestart = now - lastRestartTime;

                    if (timeSinceLastRestart > RESTART_COOLDOWN) {
                        log('🔄 Auto-restarting...', 'info');
                        scheduleRestart();
                    } else {
                        log('⏳ Restart cooldown active', 'warning');
                    }
                }
            };
            
            isActive = true;
            
            try {
                recognition.start();
                log('🚀 Starting speech recognition...', 'info');
            } catch (error) {
                log(`❌ Failed to start: ${error.message}`, 'error');
                updateStatus('Failed to start recognition', 'error');
            }
        }
        
        function stopTest() {
            isActive = false;
            
            if (restartTimeout) {
                clearTimeout(restartTimeout);
                restartTimeout = null;
            }
            
            retryCount = 0;
            
            if (recognition) {
                try {
                    recognition.stop();
                } catch (error) {
                    log(`⚠️ Stop error: ${error.message}`, 'warning');
                }
                recognition = null;
            }
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('startBtn').classList.remove('recording');
            document.getElementById('stopBtn').disabled = true;
            
            updateStatus('Stopped', 'info');
            log('🛑 Test stopped', 'info');
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('transcript').textContent = 'Speak something to see the transcript here...';
        }
        
        // Initialize
        log('🔧 Speech Recognition Fix Test initialized', 'success');
        updateStatus('Click "Start Speech Recognition" to begin testing', 'info');
    </script>
</body>
</html>
