import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { Translation } from './entities/translation.entity';

export interface TranslateRequest {
  text: string;
  sourceLanguage: string;
  targetLanguage: string;
  userId: string;
}

export interface TranslateResponse {
  originalText: string;
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  isQuestion: boolean;
  aiAnswer?: string;
}

@Injectable()
export class TranslationService {
  private readonly logger = new Logger(TranslationService.name);
  private readonly deepseekApiKey: string;
  private readonly deepseekApiUrl: string;

  constructor(
    @InjectRepository(Translation)
    private translationRepository: Repository<Translation>,
    private configService: ConfigService,
  ) {
    this.deepseekApiKey = this.configService.get<string>('DEEPSEEK_API_KEY');
    this.deepseekApiUrl = this.configService.get<string>('DEEPSEEK_API_URL');
  }

  async translateText(request: TranslateRequest): Promise<TranslateResponse> {
    try {
      this.logger.log(`Translating text from ${request.sourceLanguage} to ${request.targetLanguage}`);

      // 调用DeepSeek API进行翻译
      const translatedText = await this.callDeepSeekTranslation(
        request.text,
        request.sourceLanguage,
        request.targetLanguage
      );

      // 检测是否为问题
      const isQuestion = await this.detectQuestion(request.text);

      // 如果是问题，生成AI回答
      let aiAnswer: string | undefined;
      if (isQuestion) {
        aiAnswer = await this.generateAnswer(request.text);
      }

      // 保存翻译记录
      const translation = await this.create({
        userId: request.userId,
        sourceText: request.text,
        targetText: translatedText,
        sourceLang: request.sourceLanguage,
        targetLang: request.targetLanguage,
        isQuestion,
        aiAnswer,
      });

      return {
        originalText: request.text,
        translatedText,
        sourceLanguage: request.sourceLanguage,
        targetLanguage: request.targetLanguage,
        isQuestion,
        aiAnswer,
      };
    } catch (error) {
      this.logger.error('Translation failed:', error);
      throw new Error('翻译服务暂时不可用，请稍后重试');
    }
  }

  private async callDeepSeekTranslation(
    text: string,
    sourceLang: string,
    targetLang: string
  ): Promise<string> {
    try {
      if (!this.deepseekApiKey || !this.deepseekApiUrl) {
        throw new Error('DeepSeek API not configured');
      }

      // 构建更专业的翻译提示词
      const systemPrompt = '你是一个专业的翻译专家，擅长中英文互译。请提供准确、自然、符合目标语言表达习惯的翻译。只返回翻译结果，不要添加任何解释、注释或额外内容。';
      const userPrompt = `请将以下${this.getLanguageName(sourceLang)}文本翻译成${this.getLanguageName(targetLang)}：\n\n${text}`;

      this.logger.log(`Translating with DeepSeek: "${text}" (${sourceLang} -> ${targetLang})`);

      const response = await axios.post(
        `${this.deepseekApiUrl}/chat/completions`,
        {
          model: 'deepseek-chat',
          messages: [
            {
              role: 'system',
              content: systemPrompt,
            },
            {
              role: 'user',
              content: userPrompt,
            },
          ],
          max_tokens: 1000,
          temperature: 0.2, // 降低温度以获得更一致的翻译
          top_p: 0.9,
          frequency_penalty: 0,
          presence_penalty: 0,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.deepseekApiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 10000, // 10秒超时
        }
      );

      if (!response.data?.choices?.[0]?.message?.content) {
        throw new Error('Invalid response from DeepSeek API');
      }

      const translatedText = response.data.choices[0].message.content.trim();

      // 验证翻译结果
      if (!translatedText || translatedText.length === 0) {
        throw new Error('Empty translation result');
      }

      this.logger.log(`DeepSeek translation result: "${translatedText}"`);
      return translatedText;

    } catch (error) {
      this.logger.error('DeepSeek translation error:', error);

      // 如果是网络错误或API错误，抛出异常
      if (error.code === 'ECONNABORTED' || error.response?.status >= 500) {
        throw new Error('翻译服务暂时不可用，请稍后重试');
      }

      // 其他错误也抛出，让上层处理
      throw new Error(`翻译失败: ${error.message}`);
    }
  }

  private async detectQuestion(text: string): Promise<boolean> {
    try {
      // 首先使用规则检测
      const questionMarkers = {
        chinese: ['？', '什么', '怎么', '为什么', '如何', '谁', '哪里', '什么时候', '哪个', '多少', '几', '能否', '可以吗', '是否'],
        english: ['?', 'what', 'how', 'why', 'who', 'where', 'when', 'which', 'can', 'could', 'would', 'should', 'is', 'are', 'do', 'does', 'did']
      };

      const lowerText = text.toLowerCase();
      const hasQuestionMark = text.includes('？') || text.includes('?');
      const hasQuestionWords = [
        ...questionMarkers.chinese,
        ...questionMarkers.english
      ].some(marker => lowerText.includes(marker.toLowerCase()));

      // 如果有明显的问句标记，直接返回true
      if (hasQuestionMark || hasQuestionWords) {
        return true;
      }

      // 如果没有明显标记，使用AI进行更精确的检测
      if (this.deepseekApiKey && this.deepseekApiUrl) {
        return await this.aiDetectQuestion(text);
      }

      return false;
    } catch (error) {
      this.logger.error('Question detection error:', error);
      // 降级到简单规则检测
      return text.includes('？') || text.includes('?');
    }
  }

  private async aiDetectQuestion(text: string): Promise<boolean> {
    try {
      const prompt = `请判断以下文本是否为问句。只回答"是"或"否"，不要添加任何解释。

文本："${text}"`;

      const response = await axios.post(
        `${this.deepseekApiUrl}/chat/completions`,
        {
          model: 'deepseek-chat',
          messages: [
            {
              role: 'system',
              content: '你是一个文本分析专家，专门判断文本是否为问句。只回答"是"或"否"。'
            },
            {
              role: 'user',
              content: prompt,
            },
          ],
          max_tokens: 10,
          temperature: 0.1,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.deepseekApiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 5000,
        }
      );

      const result = response.data.choices[0].message.content.trim();
      return result === '是' || result.toLowerCase() === 'yes';
    } catch (error) {
      this.logger.error('AI question detection error:', error);
      return false;
    }
  }

  private async generateAnswer(question: string): Promise<string> {
    try {
      if (!this.deepseekApiKey || !this.deepseekApiUrl) {
        return this.mockAnswer(question);
      }

      // 检测问题语言并构建相应的提示词
      const isChineseQuestion = /[\u4e00-\u9fa5]/.test(question);

      const systemPrompt = isChineseQuestion
        ? '你是一个知识渊博、友善的AI助手。请根据用户的问题提供准确、有用、简洁的中文回答。回答要准确可靠、简洁明了、友善礼貌。如果不确定答案，请诚实说明。'
        : 'You are a knowledgeable and friendly AI assistant. Please provide accurate, helpful, and concise answers to user questions. Your answers should be reliable, clear, and polite. If you are unsure about an answer, please be honest about it.';

      const userPrompt = isChineseQuestion
        ? `请回答以下问题：${question}`
        : `Please answer the following question: ${question}`;

      this.logger.log(`Generating AI answer for: "${question}"`);

      const response = await axios.post(
        `${this.deepseekApiUrl}/chat/completions`,
        {
          model: 'deepseek-chat',
          messages: [
            {
              role: 'system',
              content: systemPrompt,
            },
            {
              role: 'user',
              content: userPrompt,
            },
          ],
          max_tokens: 500,
          temperature: 0.7,
          top_p: 0.9,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.deepseekApiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 15000,
        }
      );

      if (!response.data?.choices?.[0]?.message?.content) {
        throw new Error('Invalid response from DeepSeek API');
      }

      const answer = response.data.choices[0].message.content.trim();

      if (!answer || answer.length === 0) {
        throw new Error('Empty answer from AI');
      }

      this.logger.log(`Generated AI answer: "${answer.substring(0, 100)}..."`);
      return answer;

    } catch (error) {
      this.logger.error('AI answer generation failed:', error);
      return this.mockAnswer(question);
    }
  }

  private mockAnswer(question: string): string {
    // 检测问题语言
    const isChineseQuestion = /[\u4e00-\u9fa5]/.test(question);

    // 根据语言返回相应的默认回答
    if (isChineseQuestion) {
      return '感谢您的提问。这是一个很好的问题，我会尽力为您提供帮助。如果您需要更具体的信息，请提供更多详细内容。';
    } else {
      return 'Thank you for your question. This is a great question, and I will do my best to help you. If you need more specific information, please provide more details.';
    }
  }

  private getLanguageName(langCode: string): string {
    const languageMap = {
      'zh-CN': '中文',
      'en-US': '英文',
      'ja-JP': '日文',
      'ko-KR': '韩文',
      'zh': '中文',
      'en': '英文',
      'ja': '日文',
      'ko': '韩文',
    };
    return languageMap[langCode] || langCode;
  }

  async create(translationData: Partial<Translation>): Promise<Translation> {
    const translation = this.translationRepository.create(translationData);
    return await this.translationRepository.save(translation);
  }

  async findByUser(userId: string, limit = 50): Promise<Translation[]> {
    return await this.translationRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  async getTranslationStats(): Promise<{
    total: number;
    today: number;
    questions: number;
  }> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const [total, todayCount, questions] = await Promise.all([
      this.translationRepository.count(),
      this.translationRepository.count({
        where: {
          createdAt: new Date(today.getTime()),
        },
      }),
      this.translationRepository.count({
        where: { isQuestion: true },
      }),
    ]);

    return { total, today: todayCount, questions };
  }
}
