"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CouponService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const coupon_entity_1 = require("./entities/coupon.entity");
let CouponService = class CouponService {
    constructor(couponRepository) {
        this.couponRepository = couponRepository;
    }
    async findAll() {
        return await this.couponRepository.find({
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id) {
        const coupon = await this.couponRepository.findOne({ where: { id } });
        if (!coupon) {
            throw new common_1.NotFoundException('优惠码不存在');
        }
        return coupon;
    }
    async validateCoupon(code) {
        const coupon = await this.couponRepository.findOne({
            where: { code, status: coupon_entity_1.CouponStatus.ACTIVE },
        });
        if (!coupon) {
            return { valid: false, message: '优惠码不存在或已失效' };
        }
        if (coupon.expiresAt && coupon.expiresAt < new Date()) {
            return { valid: false, message: '优惠码已过期' };
        }
        if (coupon.usageLimit && coupon.usedCount >= coupon.usageLimit) {
            return { valid: false, message: '优惠码使用次数已达上限' };
        }
        return { valid: true, coupon };
    }
    async useCoupon(code) {
        const validation = await this.validateCoupon(code);
        if (!validation.valid) {
            throw new common_1.BadRequestException(validation.message);
        }
        const coupon = validation.coupon;
        coupon.usedCount += 1;
        return await this.couponRepository.save(coupon);
    }
    async create(createCouponDto) {
        const existingCoupon = await this.couponRepository.findOne({
            where: { code: createCouponDto.code },
        });
        if (existingCoupon) {
            throw new common_1.BadRequestException('优惠码已存在');
        }
        const coupon = this.couponRepository.create(createCouponDto);
        return await this.couponRepository.save(coupon);
    }
    async createBatch(count, template) {
        const coupons = [];
        for (let i = 0; i < count; i++) {
            const code = this.generateCouponCode();
            const coupon = this.couponRepository.create({
                ...template,
                code,
                name: `${template.name}_${i + 1}`,
            });
            coupons.push(coupon);
        }
        return await this.couponRepository.save(coupons);
    }
    async generateRandomCoupons(count, discountType, discountValue, options = {}) {
        const coupons = [];
        for (let i = 0; i < count; i++) {
            const code = this.generateCouponCode();
            const coupon = this.couponRepository.create({
                code,
                name: options.name || `随机优惠码_${i + 1}`,
                description: options.description || '系统生成的随机优惠码',
                discountType,
                discountValue,
                usageLimit: options.usageLimit,
                minOrderAmount: options.minOrderAmount,
                expiresAt: options.expiresAt,
                status: coupon_entity_1.CouponStatus.ACTIVE,
            });
            coupons.push(coupon);
        }
        return await this.couponRepository.save(coupons);
    }
    async update(id, updateCouponDto) {
        const coupon = await this.couponRepository.findOne({ where: { id } });
        if (!coupon) {
            throw new common_1.NotFoundException('优惠码不存在');
        }
        if (updateCouponDto.code && updateCouponDto.code !== coupon.code) {
            const existingCoupon = await this.couponRepository.findOne({
                where: { code: updateCouponDto.code },
            });
            if (existingCoupon) {
                throw new common_1.BadRequestException('优惠码已存在');
            }
        }
        Object.assign(coupon, updateCouponDto);
        return await this.couponRepository.save(coupon);
    }
    async remove(id) {
        const coupon = await this.couponRepository.findOne({ where: { id } });
        if (!coupon) {
            throw new common_1.NotFoundException('优惠码不存在');
        }
        await this.couponRepository.remove(coupon);
    }
    async toggleStatus(id) {
        const coupon = await this.couponRepository.findOne({ where: { id } });
        if (!coupon) {
            throw new common_1.NotFoundException('优惠码不存在');
        }
        coupon.status = coupon.status === coupon_entity_1.CouponStatus.ACTIVE
            ? coupon_entity_1.CouponStatus.INACTIVE
            : coupon_entity_1.CouponStatus.ACTIVE;
        return await this.couponRepository.save(coupon);
    }
    async getStats() {
        const total = await this.couponRepository.count();
        const active = await this.couponRepository.count({
            where: { status: coupon_entity_1.CouponStatus.ACTIVE },
        });
        const inactive = await this.couponRepository.count({
            where: { status: coupon_entity_1.CouponStatus.INACTIVE },
        });
        const expired = await this.couponRepository.count({
            where: {
                status: coupon_entity_1.CouponStatus.ACTIVE,
                expiresAt: (0, typeorm_2.Between)(new Date('1970-01-01'), new Date()),
            },
        });
        const usageStats = await this.couponRepository
            .createQueryBuilder('coupon')
            .select('SUM(coupon.usedCount)', 'totalUsed')
            .addSelect('AVG(coupon.usedCount)', 'avgUsed')
            .getRawOne();
        const discountTypeStats = await this.couponRepository
            .createQueryBuilder('coupon')
            .select('coupon.discountType', 'type')
            .addSelect('COUNT(*)', 'count')
            .groupBy('coupon.discountType')
            .getRawMany();
        return {
            total,
            active,
            inactive,
            expired,
            totalUsed: parseInt(usageStats?.totalUsed || '0'),
            avgUsed: parseFloat(usageStats?.avgUsed || '0'),
            discountTypeStats: discountTypeStats.map(stat => ({
                type: stat.type,
                count: parseInt(stat.count),
            })),
        };
    }
    generateCouponCode() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 8; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
};
exports.CouponService = CouponService;
exports.CouponService = CouponService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(coupon_entity_1.Coupon)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], CouponService);
//# sourceMappingURL=coupon.service.js.map