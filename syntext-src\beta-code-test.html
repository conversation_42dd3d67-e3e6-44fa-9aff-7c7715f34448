<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内测验证码功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        
        .beta-notice {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .form-group {
            margin: 20px 0;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        input[type="text"], input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        input[type="text"]:focus, input[type="email"]:focus {
            border-color: #007bff;
            outline: none;
        }
        
        .beta-input {
            border-color: #ffc107 !important;
            background-color: #fffbf0;
        }
        
        .beta-input:focus {
            border-color: #ff8c00 !important;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .test-results {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 内测验证码功能测试</h1>
        <p>测试新添加的内测验证码功能是否正常工作</p>
        
        <div class="beta-notice">
            <h3>⚠️ 内测阶段</h3>
            <p>系统当前处于内测阶段，需要输入正确的内测验证码才能继续使用。</p>
            <p><strong>正确的内测验证码：</strong> <code>weilynCHENliuYU</code></p>
            <p><strong>配置方式：</strong></p>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>前端环境变量: <code>VITE_BETA_MODE=true</code>, <code>VITE_BETA_CODE=weilynCHENliuYU</code></li>
                <li>后端环境变量: <code>BETA_MODE=true</code>, <code>BETA_CODE=weilynCHENliuYU</code></li>
            </ul>
        </div>
        
        <div id="status" class="status info">准备测试内测验证码功能</div>
        
        <form id="testForm">
            <div class="form-group">
                <label for="betaCode">内测验证码 <span style="color: red;">*</span></label>
                <input type="text" id="betaCode" class="beta-input" placeholder="请输入内测验证码" required>
                <small style="color: #666;">请联系管理员获取内测验证码</small>
            </div>
            
            <div class="form-group">
                <label for="email">邮箱地址</label>
                <input type="email" id="email" placeholder="请输入您的邮箱" required>
            </div>
            
            <div class="form-group">
                <label for="code">邮箱验证码</label>
                <input type="text" id="code" placeholder="请输入6位验证码" maxlength="6" required>
            </div>
            
            <button type="submit">测试登录</button>
            <button type="button" onclick="runTests()">运行自动测试</button>
            <button type="button" onclick="clearResults()">清除结果</button>
        </form>
        
        <div class="test-results" id="testResults">
            <h3>测试结果</h3>
            <div id="results">等待测试...</div>
        </div>
    </div>

    <script>
        const CORRECT_BETA_CODE = 'weilynCHENliuYU';
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultItem = document.createElement('div');
            resultItem.className = `status ${type}`;
            resultItem.style.margin = '10px 0';
            resultItem.textContent = message;
            resultsDiv.appendChild(resultItem);
        }
        
        function validateBetaCode(betaCode) {
            return betaCode === CORRECT_BETA_CODE;
        }
        
        function testBetaCodeValidation() {
            addResult('开始测试内测验证码验证功能...', 'info');
            
            // 测试1: 正确的验证码
            const correctResult = validateBetaCode(CORRECT_BETA_CODE);
            if (correctResult) {
                addResult('✅ 测试1通过: 正确的内测验证码验证成功', 'success');
            } else {
                addResult('❌ 测试1失败: 正确的内测验证码验证失败', 'error');
            }
            
            // 测试2: 错误的验证码
            const wrongResult = validateBetaCode('wrongcode');
            if (!wrongResult) {
                addResult('✅ 测试2通过: 错误的内测验证码被正确拒绝', 'success');
            } else {
                addResult('❌ 测试2失败: 错误的内测验证码被错误接受', 'error');
            }
            
            // 测试3: 空验证码
            const emptyResult = validateBetaCode('');
            if (!emptyResult) {
                addResult('✅ 测试3通过: 空内测验证码被正确拒绝', 'success');
            } else {
                addResult('❌ 测试3失败: 空内测验证码被错误接受', 'error');
            }
            
            // 测试4: 大小写敏感性
            const caseResult = validateBetaCode('WEILYNCHENLIUYU');
            if (!caseResult) {
                addResult('✅ 测试4通过: 大小写不匹配的验证码被正确拒绝', 'success');
            } else {
                addResult('❌ 测试4失败: 大小写不匹配的验证码被错误接受', 'error');
            }
            
            addResult('内测验证码验证功能测试完成', 'info');
        }
        
        function runTests() {
            document.getElementById('results').innerHTML = '';
            addResult('开始运行自动测试...', 'info');
            
            setTimeout(() => {
                testBetaCodeValidation();
                addResult('所有测试完成！', 'success');
            }, 500);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '等待测试...';
            updateStatus('准备测试内测验证码功能', 'info');
        }
        
        // 表单提交处理
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const betaCode = document.getElementById('betaCode').value;
            const email = document.getElementById('email').value;
            const code = document.getElementById('code').value;
            
            // 验证内测验证码
            if (!validateBetaCode(betaCode)) {
                updateStatus('内测验证码错误，请联系管理员获取正确的验证码', 'error');
                addResult(`❌ 登录失败: 内测验证码 "${betaCode}" 不正确`, 'error');
                return;
            }
            
            if (!email || !code) {
                updateStatus('请输入邮箱和验证码', 'warning');
                return;
            }
            
            updateStatus('内测验证码验证通过！', 'success');
            addResult(`✅ 内测验证码验证通过: "${betaCode}"`, 'success');
            addResult(`📧 邮箱: ${email}`, 'info');
            addResult(`🔢 验证码: ${code}`, 'info');
            addResult('在实际系统中，此时会发送请求到后端进行邮箱验证码验证', 'info');
        });
        
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            addResult('内测验证码功能测试页面已加载', 'success');
            addResult(`正确的内测验证码: ${CORRECT_BETA_CODE}`, 'info');
        });
    </script>
</body>
</html>
