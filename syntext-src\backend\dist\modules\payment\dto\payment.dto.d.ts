import { PaymentMethod } from '../entities/payment.entity';
import { PlanType } from '../../subscription/entities/subscription.entity';
export declare class CreatePaymentDto {
    planType: PlanType;
    paymentMethod: PaymentMethod;
    couponCode?: string;
    returnUrl?: string;
}
export declare class PaymentCallbackDto {
    callbackData: any;
}
export declare class PriceCalculationDto {
    originalPrice: number;
    discountAmount: number;
    finalPrice: number;
    couponInfo?: {
        code: string;
        name: string;
        discountType: string;
        discountValue: number;
    };
}
export declare class PaymentOrderDto {
    id: string;
    orderNumber: string;
    paymentMethod: PaymentMethod;
    status: string;
    amount: number;
    originalAmount: number;
    discountAmount: number;
    currency: string;
    description: string;
    paymentUrl?: string;
    thirdPartyOrderId?: string;
    createdAt: Date;
    paidAt?: Date;
}
export declare class PaymentStatsDto {
    totalOrders: number;
    successOrders: number;
    failedOrders: number;
    pendingOrders: number;
    totalRevenue: number;
    monthlyRevenue: number;
    dailyRevenue: number;
    paymentMethodStats: Array<{
        method: PaymentMethod;
        count: number;
        revenue: number;
    }>;
}
