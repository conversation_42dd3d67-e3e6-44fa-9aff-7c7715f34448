{"context":"NestApplication","level":"error","message":"Error: listen EADDRINUSE: address already in use :::3000","stack":[null],"timestamp":"2025-08-04T14:10:29.640Z"}
{"context":"SpeechService","level":"error","message":"DeepSeek Speech API error:","stack":[{"code":"ERR_OUT_OF_RANGE"}],"timestamp":"2025-08-04T17:00:59.975Z"}
{"context":"SpeechService","level":"error","message":"Speech to text failed:","stack":[{"code":"ERR_OUT_OF_RANGE"}],"timestamp":"2025-08-04T17:00:59.975Z"}
{"context":"SpeechService","level":"error","message":"DeepSeek Speech API error:","stack":[{"code":"ERR_OUT_OF_RANGE"}],"timestamp":"2025-08-04T17:01:00.040Z"}
{"context":"SpeechService","level":"error","message":"Speech to text failed:","stack":[{"code":"ERR_OUT_OF_RANGE"}],"timestamp":"2025-08-04T17:01:00.040Z"}
{"context":"NestApplication","level":"error","message":"Error: listen EADDRINUSE: address already in use :::3000","stack":[null],"timestamp":"2025-08-04T17:56:26.430Z"}
