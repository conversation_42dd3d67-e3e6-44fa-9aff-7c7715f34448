{"schema": "1.0", "comment": "This file maps proto services/RPCs to the corresponding library clients/methods", "language": "typescript", "protoPackage": "google.cloud.speech.v1p1beta1", "libraryPackage": "@google-cloud/speech", "services": {"Adaptation": {"clients": {"grpc": {"libraryClient": "AdaptationClient", "rpcs": {"CreatePhraseSet": {"methods": ["createPhraseSet"]}, "GetPhraseSet": {"methods": ["getPhraseSet"]}, "UpdatePhraseSet": {"methods": ["updatePhraseSet"]}, "DeletePhraseSet": {"methods": ["deletePhraseSet"]}, "CreateCustomClass": {"methods": ["createCustomClass"]}, "GetCustomClass": {"methods": ["getCustomClass"]}, "UpdateCustomClass": {"methods": ["updateCustomClass"]}, "DeleteCustomClass": {"methods": ["deleteCustomClass"]}, "ListPhraseSet": {"methods": ["listPhraseSet", "listPhraseSetStream", "listPhraseSetAsync"]}, "ListCustomClasses": {"methods": ["listCustomClasses", "listCustomClassesStream", "listCustomClassesAsync"]}}}, "grpc-fallback": {"libraryClient": "AdaptationClient", "rpcs": {"CreatePhraseSet": {"methods": ["createPhraseSet"]}, "GetPhraseSet": {"methods": ["getPhraseSet"]}, "UpdatePhraseSet": {"methods": ["updatePhraseSet"]}, "DeletePhraseSet": {"methods": ["deletePhraseSet"]}, "CreateCustomClass": {"methods": ["createCustomClass"]}, "GetCustomClass": {"methods": ["getCustomClass"]}, "UpdateCustomClass": {"methods": ["updateCustomClass"]}, "DeleteCustomClass": {"methods": ["deleteCustomClass"]}, "ListPhraseSet": {"methods": ["listPhraseSet", "listPhraseSetStream", "listPhraseSetAsync"]}, "ListCustomClasses": {"methods": ["listCustomClasses", "listCustomClassesStream", "listCustomClassesAsync"]}}}}}, "Speech": {"clients": {"grpc": {"libraryClient": "SpeechClient", "rpcs": {"Recognize": {"methods": ["recognize"]}, "StreamingRecognize": {"methods": ["streamingRecognize"]}, "LongRunningRecognize": {"methods": ["longRunningRecognize"]}}}, "grpc-fallback": {"libraryClient": "SpeechClient", "rpcs": {"Recognize": {"methods": ["recognize"]}, "LongRunningRecognize": {"methods": ["longRunningRecognize"]}}}}}}}