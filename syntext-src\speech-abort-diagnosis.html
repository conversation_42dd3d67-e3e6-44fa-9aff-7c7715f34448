<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Speech Recognition Abort Diagnosis</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        
        .recording { background: #dc3545 !important; animation: pulse 1s infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.7; } }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .diagnosis {
            background: #e7f3ff;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Speech Recognition Abort Diagnosis</h1>
        <p>This tool helps diagnose why speech recognition gets aborted immediately after starting.</p>
        
        <div id="status" class="status info">Ready for diagnosis</div>
        
        <div>
            <button id="diagnoseBtn" onclick="runDiagnosis()">Run Full Diagnosis</button>
            <button id="testBtn" onclick="testBasicRecognition()">Test Basic Recognition</button>
            <button id="testPermBtn" onclick="testPermissions()">Test Permissions</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="grid">
            <div class="diagnosis">
                <h3>🔧 Environment Check</h3>
                <div id="envCheck">Running checks...</div>
            </div>
            <div class="diagnosis">
                <h3>🎤 Permission Status</h3>
                <div id="permStatus">Checking permissions...</div>
            </div>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let recognition = null;
        let diagnosticResults = {};
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666">[${timestamp}]</span> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        async function checkEnvironment() {
            const envDiv = document.getElementById('envCheck');
            let results = [];
            
            // Check HTTPS
            const isSecure = window.isSecureContext;
            results.push(`🔒 Secure Context: ${isSecure ? '✅ Yes' : '❌ No'}`);
            diagnosticResults.secureContext = isSecure;
            
            // Check focus
            const hasFocus = document.hasFocus();
            results.push(`👁️ Document Focus: ${hasFocus ? '✅ Yes' : '⚠️ No'}`);
            diagnosticResults.documentFocus = hasFocus;
            
            // Check Speech Recognition support
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const hasSupport = !!SpeechRecognition;
            results.push(`🎤 Speech Recognition: ${hasSupport ? '✅ Supported' : '❌ Not Supported'}`);
            diagnosticResults.speechSupport = hasSupport;
            
            // Check user agent
            const isChrome = /Chrome/.test(navigator.userAgent);
            results.push(`🌐 Chrome Browser: ${isChrome ? '✅ Yes' : '⚠️ Other'}`);
            diagnosticResults.isChrome = isChrome;
            
            // Check if page was loaded via user interaction
            results.push(`👆 User Activation: ${navigator.userActivation ? '✅ Available' : '⚠️ Not Available'}`);
            
            envDiv.innerHTML = results.join('<br>');
            return diagnosticResults;
        }
        
        async function checkPermissions() {
            const permDiv = document.getElementById('permStatus');
            let results = [];
            
            try {
                // Check microphone permission
                const micPermission = await navigator.permissions.query({ name: 'microphone' });
                results.push(`🎤 Microphone: ${micPermission.state}`);
                diagnosticResults.microphonePermission = micPermission.state;
                
                // Try to get media stream
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    results.push(`📡 Media Stream: ✅ Accessible`);
                    stream.getTracks().forEach(track => track.stop());
                    diagnosticResults.mediaStreamAccess = true;
                } catch (error) {
                    results.push(`📡 Media Stream: ❌ ${error.message}`);
                    diagnosticResults.mediaStreamAccess = false;
                }
                
            } catch (error) {
                results.push(`❌ Permission check failed: ${error.message}`);
                diagnosticResults.permissionCheckFailed = true;
            }
            
            permDiv.innerHTML = results.join('<br>');
            return diagnosticResults;
        }
        
        async function testBasicRecognition() {
            log('🧪 Testing basic speech recognition...', 'info');
            updateStatus('Testing basic recognition...', 'info');
            
            if (!window.SpeechRecognition && !window.webkitSpeechRecognition) {
                log('❌ Speech Recognition not supported', 'error');
                updateStatus('Speech Recognition not supported', 'error');
                return;
            }
            
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            recognition = new SpeechRecognition();
            
            // Configure recognition
            recognition.continuous = false; // Start with non-continuous
            recognition.interimResults = false; // Start with final results only
            recognition.lang = 'en-US';
            
            let startTime = Date.now();
            let hasStarted = false;
            
            recognition.onstart = () => {
                hasStarted = true;
                const timeTaken = Date.now() - startTime;
                log(`✅ Recognition started successfully (${timeTaken}ms)`, 'success');
                updateStatus('Recognition started - speak now!', 'success');
            };
            
            recognition.onresult = (event) => {
                for (let i = 0; i < event.results.length; i++) {
                    const result = event.results[i];
                    log(`🎯 Result: "${result[0].transcript}" (confidence: ${result[0].confidence})`, 'success');
                }
            };
            
            recognition.onerror = (event) => {
                const timeTaken = Date.now() - startTime;
                log(`❌ Recognition error: ${event.error} (${timeTaken}ms after start)`, 'error');
                
                if (event.error === 'aborted') {
                    log(`🔍 Abort analysis:`, 'warning');
                    log(`  - Started successfully: ${hasStarted}`, 'warning');
                    log(`  - Time to abort: ${timeTaken}ms`, 'warning');
                    log(`  - Likely causes:`, 'warning');
                    if (timeTaken < 100) {
                        log(`    • Immediate abort - browser security restriction`, 'warning');
                    } else if (timeTaken < 1000) {
                        log(`    • Quick abort - permission or resource conflict`, 'warning');
                    } else {
                        log(`    • Delayed abort - user or system interruption`, 'warning');
                    }
                }
                
                updateStatus(`Recognition error: ${event.error}`, 'error');
            };
            
            recognition.onend = () => {
                const timeTaken = Date.now() - startTime;
                log(`🛑 Recognition ended (${timeTaken}ms total)`, 'info');
                updateStatus('Recognition ended', 'info');
            };
            
            try {
                log('🚀 Starting recognition...', 'info');
                recognition.start();
            } catch (error) {
                log(`❌ Failed to start recognition: ${error.message}`, 'error');
                updateStatus(`Failed to start: ${error.message}`, 'error');
            }
        }
        
        async function testPermissions() {
            log('🔐 Testing permissions...', 'info');
            updateStatus('Testing permissions...', 'info');
            
            try {
                // Test microphone access
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });
                
                log('✅ Microphone access granted', 'success');
                
                // Get audio track info
                const audioTrack = stream.getAudioTracks()[0];
                if (audioTrack) {
                    const settings = audioTrack.getSettings();
                    log(`🎤 Audio track settings: ${JSON.stringify(settings)}`, 'info');
                }
                
                // Clean up
                stream.getTracks().forEach(track => track.stop());
                
                updateStatus('Permissions test passed', 'success');
                
            } catch (error) {
                log(`❌ Permission test failed: ${error.message}`, 'error');
                updateStatus(`Permission test failed: ${error.message}`, 'error');
            }
        }
        
        async function runDiagnosis() {
            log('🔍 Starting full diagnosis...', 'info');
            updateStatus('Running full diagnosis...', 'info');
            
            // Run all checks
            await checkEnvironment();
            await checkPermissions();
            
            // Analyze results
            log('📊 Diagnosis Results:', 'info');
            
            if (!diagnosticResults.secureContext) {
                log('⚠️ CRITICAL: Not in secure context (HTTPS required)', 'error');
            }
            
            if (!diagnosticResults.speechSupport) {
                log('⚠️ CRITICAL: Speech Recognition not supported', 'error');
            }
            
            if (diagnosticResults.microphonePermission === 'denied') {
                log('⚠️ CRITICAL: Microphone permission denied', 'error');
            }
            
            if (!diagnosticResults.mediaStreamAccess) {
                log('⚠️ WARNING: Cannot access media stream', 'warning');
            }
            
            if (!diagnosticResults.documentFocus) {
                log('⚠️ INFO: Document not focused (may affect recognition)', 'info');
            }
            
            updateStatus('Diagnosis complete - check log for details', 'success');
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Initialize
        window.addEventListener('load', () => {
            log('🔧 Speech Recognition Diagnosis Tool loaded', 'success');
            checkEnvironment();
            checkPermissions();
        });
        
        // Focus handler
        window.addEventListener('focus', () => {
            log('👁️ Window gained focus', 'info');
        });
        
        window.addEventListener('blur', () => {
            log('👁️ Window lost focus', 'warning');
        });
    </script>
</body>
</html>
