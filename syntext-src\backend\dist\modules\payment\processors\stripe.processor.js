"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var StripeProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.StripeProcessor = void 0;
const common_1 = require("@nestjs/common");
const payment_entity_1 = require("../entities/payment.entity");
let StripeProcessor = StripeProcessor_1 = class StripeProcessor {
    constructor() {
        this.logger = new common_1.Logger(StripeProcessor_1.name);
        this.paymentMethod = payment_entity_1.PaymentMethod.STRIPE;
        this.config = {
            secretKey: process.env.STRIPE_SECRET_KEY || 'sk_test_mock_key',
            publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || 'pk_test_mock_key',
            webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || 'whsec_mock_secret',
            apiVersion: '2023-10-16',
        };
    }
    async createPayment(params) {
        try {
            this.logger.log(`Creating Stripe payment for order: ${params.orderNumber}`);
            if (this.isProductionConfigured()) {
                return await this.createRealPayment(params);
            }
            else {
                return await this.createMockPayment(params);
            }
        }
        catch (error) {
            this.logger.error('Failed to create Stripe payment:', error);
            return {
                success: false,
                error: '创建Stripe支付失败',
            };
        }
    }
    async verifyCallback(callbackData) {
        try {
            this.logger.log('Verifying Stripe webhook');
            if (this.isProductionConfigured()) {
                return await this.verifyRealCallback(callbackData);
            }
            else {
                return await this.verifyMockCallback(callbackData);
            }
        }
        catch (error) {
            this.logger.error('Failed to verify Stripe webhook:', error);
            return {
                verified: false,
                error: '回调验证失败',
            };
        }
    }
    async queryPaymentStatus(thirdPartyOrderId) {
        try {
            this.logger.log(`Querying Stripe payment status: ${thirdPartyOrderId}`);
            if (this.isProductionConfigured()) {
                return await this.queryRealPaymentStatus(thirdPartyOrderId);
            }
            else {
                return await this.queryMockPaymentStatus(thirdPartyOrderId);
            }
        }
        catch (error) {
            this.logger.error('Failed to query Stripe payment status:', error);
            return {
                success: false,
                error: '查询支付状态失败',
            };
        }
    }
    async refund(params) {
        try {
            this.logger.log(`Processing Stripe refund: ${params.refundOrderNumber}`);
            if (this.isProductionConfigured()) {
                return await this.processRealRefund(params);
            }
            else {
                return await this.processMockRefund(params);
            }
        }
        catch (error) {
            this.logger.error('Failed to process Stripe refund:', error);
            return {
                success: false,
                error: '退款处理失败',
            };
        }
    }
    isProductionConfigured() {
        return (this.config.secretKey !== 'sk_test_mock_key' &&
            this.config.publishableKey !== 'pk_test_mock_key' &&
            this.config.webhookSecret !== 'whsec_mock_secret');
    }
    async createRealPayment(params) {
        this.logger.warn('Real Stripe payment not implemented yet');
        return {
            success: false,
            error: 'Stripe支付功能正在开发中，敬请期待',
        };
    }
    async createMockPayment(params) {
        const thirdPartyOrderId = `pi_mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const paymentUrl = `https://checkout.stripe.com/pay/${thirdPartyOrderId}`;
        this.logger.log(`Mock Stripe payment created: ${thirdPartyOrderId}`);
        return {
            success: true,
            thirdPartyOrderId,
            paymentUrl,
            extraData: {
                mockPayment: true,
                message: '这是模拟支付，实际不会扣费',
                clientSecret: `${thirdPartyOrderId}_secret_mock`,
                publishableKey: this.config.publishableKey,
            },
        };
    }
    async verifyRealCallback(callbackData) {
        this.logger.warn('Real Stripe webhook verification not implemented yet');
        return {
            verified: false,
            error: 'Webhook验证功能正在开发中',
        };
    }
    async verifyMockCallback(callbackData) {
        if (callbackData && callbackData.type === 'payment_intent.succeeded') {
            const paymentIntent = callbackData.data?.object;
            return {
                verified: true,
                thirdPartyOrderId: paymentIntent?.id,
                paymentStatus: 'success',
                amount: paymentIntent?.amount,
                rawData: callbackData,
            };
        }
        return {
            verified: false,
            error: '模拟Webhook验证失败',
        };
    }
    async queryRealPaymentStatus(thirdPartyOrderId) {
        this.logger.warn('Real Stripe payment status query not implemented yet');
        return {
            success: false,
            error: '支付状态查询功能正在开发中',
        };
    }
    async queryMockPaymentStatus(thirdPartyOrderId) {
        if (thirdPartyOrderId.startsWith('pi_mock_')) {
            const statuses = ['success', 'pending', 'failed'];
            const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
            return {
                success: true,
                status: randomStatus,
                amount: Math.floor(Math.random() * 10000) + 1000,
                paidAt: randomStatus === 'success' ? new Date() : undefined,
            };
        }
        return {
            success: false,
            error: '订单不存在',
        };
    }
    async processRealRefund(params) {
        this.logger.warn('Real Stripe refund not implemented yet');
        return {
            success: false,
            error: '退款功能正在开发中',
        };
    }
    async processMockRefund(params) {
        const thirdPartyRefundId = `re_mock_${Date.now()}`;
        this.logger.log(`Mock Stripe refund processed: ${thirdPartyRefundId}`);
        return {
            success: true,
            thirdPartyRefundId,
        };
    }
};
exports.StripeProcessor = StripeProcessor;
exports.StripeProcessor = StripeProcessor = StripeProcessor_1 = __decorate([
    (0, common_1.Injectable)()
], StripeProcessor);
//# sourceMappingURL=stripe.processor.js.map