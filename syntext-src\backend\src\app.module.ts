import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WinstonModule } from 'nest-winston';
import { ConfigModule } from '@nestjs/config';
import * as winston from 'winston';

import { DatabaseConfig } from './config/database.config';
import { AppController } from './app.controller';
import { AuthModule } from './auth/auth.module';
import { UserModule } from './modules/user/user.module';
import { TranslationModule } from './modules/translation/translation.module';
import { SubscriptionModule } from './modules/subscription/subscription.module';
import { CouponModule } from './modules/coupon/coupon.module';
import { SpeechModule } from './modules/speech/speech.module';

@Module({
  controllers: [AppController],
  imports: [
    // 环境配置
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env', '../.env'],
    }),

    // 数据库配置
    TypeOrmModule.forRootAsync({
      useClass: DatabaseConfig,
    }),

    // 日志配置
    WinstonModule.forRoot({
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.colorize(),
            winston.format.simple(),
          ),
        }),
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json(),
          ),
        }),
        new winston.transports.File({
          filename: 'logs/combined.log',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json(),
          ),
        }),
      ],
    }),

    // 业务模块
    AuthModule,
    UserModule,
    TranslationModule,
    SubscriptionModule,
    CouponModule,
    SpeechModule,
  ],
})
export class AppModule {}
