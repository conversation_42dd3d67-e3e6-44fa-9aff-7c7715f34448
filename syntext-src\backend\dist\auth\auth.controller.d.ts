import { AuthService } from './auth.service';
import { SendCodeDto } from './dto/send-code.dto';
import { VerifyCodeDto } from './dto/verify-code.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    getAuthInfo(): {
        service: string;
        version: string;
        status: string;
        endpoints: string[];
    };
    sendCode(sendCodeDto: SendCodeDto): Promise<{
        message: string;
    }>;
    verifyCode(verifyCodeDto: VerifyCodeDto): Promise<{
        access_token: string;
        user: import("../modules/user/entities/user.entity").User;
    }>;
    refresh(req: any): Promise<{
        access_token: string;
    }>;
    getProfile(req: any): Promise<import("../modules/user/entities/user.entity").User>;
}
