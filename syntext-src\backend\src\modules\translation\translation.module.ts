import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { Translation } from './entities/translation.entity';
import { TranslationService } from './translation.service';
import { TranslationController } from './translation.controller';
import { TranslationGateway } from './translation.gateway';
import { SpeechModule } from '../speech/speech.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Translation]),
    ConfigModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'your-jwt-secret-key',
      signOptions: { expiresIn: '7d' },
    }),
    SpeechModule,
  ],
  controllers: [TranslationController],
  providers: [TranslationService, TranslationGateway],
  exports: [TranslationService],
})
export class TranslationModule {}
