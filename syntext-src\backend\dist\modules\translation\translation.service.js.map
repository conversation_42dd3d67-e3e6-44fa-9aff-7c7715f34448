{"version": 3, "file": "translation.service.js", "sourceRoot": "", "sources": ["../../../src/modules/translation/translation.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAAqC;AACrC,2CAA+C;AAC/C,iCAA0B;AAC1B,sEAA4D;AAmBrD,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAK7B,YAEE,qBAAsD,EAC9C,aAA4B;QAD5B,0BAAqB,GAArB,qBAAqB,CAAyB;QAC9C,kBAAa,GAAb,aAAa,CAAe;QAPrB,WAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;QAS5D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,kBAAkB,CAAC,CAAC;QACzE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,kBAAkB,CAAC,CAAC;IAC3E,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,OAAyB;QAC3C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,OAAO,CAAC,cAAc,OAAO,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC;YAGhG,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,uBAAuB,CACvD,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,cAAc,CACvB,CAAC;YAGF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAG3D,IAAI,QAA4B,CAAC;YACjC,IAAI,UAAU,EAAE,CAAC;gBACf,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACrD,CAAC;YAGD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC;gBACpC,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,UAAU,EAAE,OAAO,CAAC,IAAI;gBACxB,UAAU,EAAE,cAAc;gBAC1B,UAAU,EAAE,OAAO,CAAC,cAAc;gBAClC,UAAU,EAAE,OAAO,CAAC,cAAc;gBAClC,UAAU;gBACV,QAAQ;aACT,CAAC,CAAC;YAEH,OAAO;gBACL,YAAY,EAAE,OAAO,CAAC,IAAI;gBAC1B,cAAc;gBACd,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,UAAU;gBACV,QAAQ;aACT,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CACnC,IAAY,EACZ,UAAkB,EAClB,UAAkB;QAElB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACjD,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;YACjD,CAAC;YAGD,MAAM,YAAY,GAAG,sEAAsE,CAAC;YAC5F,MAAM,UAAU,GAAG,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,QAAQ,IAAI,EAAE,CAAC;YAEjH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,IAAI,MAAM,UAAU,OAAO,UAAU,GAAG,CAAC,CAAC;YAEzF,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAC/B,GAAG,IAAI,CAAC,cAAc,mBAAmB,EACzC;gBACE,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,YAAY;qBACtB;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,UAAU;qBACpB;iBACF;gBACD,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,GAAG;gBAChB,KAAK,EAAE,GAAG;gBACV,iBAAiB,EAAE,CAAC;gBACpB,gBAAgB,EAAE,CAAC;aACpB,EACD;gBACE,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,cAAc,EAAE;oBAChD,cAAc,EAAE,kBAAkB;iBACnC;gBACD,OAAO,EAAE,KAAK;aACf,CACF,CAAC;YAEF,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;gBACnD,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACxD,CAAC;YAED,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAGvE,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACnD,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,cAAc,GAAG,CAAC,CAAC;YACpE,OAAO,cAAc,CAAC;QAExB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YAGxD,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,IAAI,GAAG,EAAE,CAAC;gBACnE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;YACrC,CAAC;YAGD,MAAM,IAAI,KAAK,CAAC,SAAS,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,IAAY;QACvC,IAAI,CAAC;YAEH,MAAM,eAAe,GAAG;gBACtB,OAAO,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;gBAC9F,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC;aAC3I,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACrC,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACjE,MAAM,gBAAgB,GAAG;gBACvB,GAAG,eAAe,CAAC,OAAO;gBAC1B,GAAG,eAAe,CAAC,OAAO;aAC3B,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YAG3D,IAAI,eAAe,IAAI,gBAAgB,EAAE,CAAC;gBACxC,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBAC/C,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC3C,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAEtD,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,IAAY;QACzC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG;;MAEf,IAAI,GAAG,CAAC;YAER,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAC/B,GAAG,IAAI,CAAC,cAAc,mBAAmB,EACzC;gBACE,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,oCAAoC;qBAC9C;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,MAAM;qBAChB;iBACF;gBACD,UAAU,EAAE,EAAE;gBACd,WAAW,EAAE,GAAG;aACjB,EACD;gBACE,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,cAAc,EAAE;oBAChD,cAAc,EAAE,kBAAkB;iBACnC;gBACD,OAAO,EAAE,IAAI;aACd,CACF,CAAC;YAEF,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAC/D,OAAO,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAgB;QAC3C,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACjD,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACnC,CAAC;YAGD,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE3D,MAAM,YAAY,GAAG,iBAAiB;gBACpC,CAAC,CAAC,2EAA2E;gBAC7E,CAAC,CAAC,+OAA+O,CAAC;YAEpP,MAAM,UAAU,GAAG,iBAAiB;gBAClC,CAAC,CAAC,WAAW,QAAQ,EAAE;gBACvB,CAAC,CAAC,yCAAyC,QAAQ,EAAE,CAAC;YAExD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,QAAQ,GAAG,CAAC,CAAC;YAE3D,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAC/B,GAAG,IAAI,CAAC,cAAc,mBAAmB,EACzC;gBACE,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,YAAY;qBACtB;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,UAAU;qBACpB;iBACF;gBACD,UAAU,EAAE,GAAG;gBACf,WAAW,EAAE,GAAG;gBAChB,KAAK,EAAE,GAAG;aACX,EACD;gBACE,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,cAAc,EAAE;oBAChD,cAAc,EAAE,kBAAkB;iBACnC;gBACD,OAAO,EAAE,KAAK;aACf,CACF,CAAC;YAEF,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;gBACnD,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACxD,CAAC;YAED,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAE/D,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAC1C,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;YACzE,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,UAAU,CAAC,QAAgB;QAEjC,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAG3D,IAAI,iBAAiB,EAAE,CAAC;YACtB,OAAO,oDAAoD,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,OAAO,+JAA+J,CAAC;QACzK,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,QAAgB;QACtC,MAAM,WAAW,GAAG;YAClB,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;SACX,CAAC;QACF,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,eAAqC;QAChD,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QACvE,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC5D,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,KAAK,GAAG,EAAE;QACzC,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YAC3C,KAAK,EAAE,EAAE,MAAM,EAAE;YACjB,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YAC5B,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,mBAAmB;QAKvB,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3B,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE,SAAS,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACvD,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE;YAClC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC;gBAC/B,KAAK,EAAE;oBACL,SAAS,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;iBACrC;aACF,CAAC;YACF,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC;gBAC/B,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;aAC5B,CAAC;SACH,CAAC,CAAC;QAEH,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;IACjD,CAAC;CACF,CAAA;AA1UY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;IAOR,WAAA,IAAA,0BAAgB,EAAC,gCAAW,CAAC,CAAA;qCACC,oBAAU;QAClB,sBAAa;GAR3B,kBAAkB,CA0U9B"}