{"version": 3, "file": "translation.gateway.js", "sourceRoot": "", "sources": ["../../../src/modules/translation/translation.gateway.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,mDAQ4B;AAC5B,yCAA2C;AAC3C,2CAAmD;AACnD,+DAA6E;AAC7E,qCAAyC;AACzC,6DAAyD;AA0BlD,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAM7B,YACmB,kBAAsC,EACtC,UAAsB,EACtB,aAA4B;QAF5B,uBAAkB,GAAlB,kBAAkB,CAAoB;QACtC,eAAU,GAAV,UAAU,CAAY;QACtB,kBAAa,GAAb,aAAa,CAAe;QAL9B,WAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;IAM3D,CAAC;IAEJ,KAAK,CAAC,gBAAgB,CAAC,MAA2B;QAChD,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK;gBAC7B,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;gBAC/D,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC;YAE3C,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2CAA2C,MAAM,CAAC,EAAE,UAAU,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC3G,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;oBACnB,OAAO,EAAE,cAAc;oBACvB,IAAI,EAAE,UAAU;oBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBACH,MAAM,CAAC,UAAU,EAAE,CAAC;gBACpB,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;YAC5B,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC;YAEjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,EAAE,UAAU,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;YAGzG,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;gBAC3B,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,KAAK,EAAE,MAAM,CAAC,SAAS;gBACvB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACrC,CAAC,CAAC;YAGH,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;gBAC3B,iBAAiB,EAAE,WAAW;gBAC9B,WAAW,EAAE,WAAW;gBACxB,IAAI,EAAE,WAAW;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3E,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;gBACnB,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE,eAAe;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YACH,MAAM,CAAC,UAAU,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAED,gBAAgB,CAAC,MAA2B;QAC1C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;IAC7E,CAAC;IAGK,AAAN,KAAK,CAAC,eAAe,CACJ,IAAsE,EAClE,MAA2B;QAE9C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;gBAC3C,OAAO;YACT,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAE9E,MAAM,OAAO,GAAqB;gBAChC,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAGpE,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAChC,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;gBACzB,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,cAAc,EAAE,MAAM,CAAC,cAAc;gBACrC,cAAc,EAAE,MAAM,CAAC,cAAc;gBACrC,cAAc,EAAE,MAAM,CAAC,cAAc;gBACrC,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,MAAM,CAAC,UAAU;aAC9B,CAAC,CAAC;YAGH,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACzC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE;oBAC7B,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;oBACzB,QAAQ,EAAE,MAAM,CAAC,YAAY;oBAC7B,MAAM,EAAE,MAAM,CAAC,QAAQ;oBACvB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,MAAM,EAAE,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,iBAAiB,CACN,IAAiE,EAC7D,MAA2B;QAE9C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAEvG,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;oBACnB,OAAO,EAAE,OAAO;oBAChB,IAAI,EAAE,cAAc;oBACpB,SAAS;oBACT,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAGD,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;oBACnB,OAAO,EAAE,gBAAgB;oBACzB,IAAI,EAAE,eAAe;oBACrB,SAAS;oBACT,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,MAAM,CAAC,SAAS,eAAe,IAAI,CAAC,QAAQ,gBAAgB,SAAS,EAAE,CAAC,CAAC;YAGzH,MAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBACpC,SAAS;gBACT,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAGH,IAAI,WAAmB,CAAC;YACxB,IAAI,CAAC;gBACH,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAGpD,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC7B,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;gBACtC,CAAC;gBAED,IAAI,WAAW,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;oBAC1C,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBAC1C,CAAC;YAEH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;gBACzD,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;oBACnB,OAAO,EAAE,aAAa;oBACtB,IAAI,EAAE,oBAAoB;oBAC1B,SAAS;oBACT,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAGD,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACnC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvF,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,eAAe,CAAC;YAEpD,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2CAA2C,SAAS,EAAE,CAAC,CAAC;gBACzE,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;oBACvC,SAAS;oBACT,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,UAAU;oBACnB,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBACtC,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,YAAY,CAAC,IAAI,kBAAkB,YAAY,CAAC,UAAU,eAAe,cAAc,KAAK,CAAC,CAAC;YAG7I,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACvC,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,UAAU,EAAE,YAAY,CAAC,UAAU;gBACnC,SAAS;gBACT,cAAc,EAAE,cAAc;gBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAGH,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;YAErE,MAAM,OAAO,GAAqB;gBAChC,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,cAAc,EAAE,IAAI,CAAC,QAAQ;gBAC7B,cAAc,EAAE,cAAc;gBAC9B,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC;YAEF,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,oBAAoB,CAAC;YAE9D,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAChC,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;gBACzB,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,cAAc,EAAE,MAAM,CAAC,cAAc;gBACrC,cAAc,EAAE,MAAM,CAAC,cAAc;gBACrC,cAAc,EAAE,MAAM,CAAC,cAAc;gBACrC,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,MAAM,CAAC,UAAU;gBAC7B,UAAU,EAAE,YAAY,CAAC,UAAU;gBACnC,SAAS;gBACT,cAAc,EAAE,mBAAmB;aACpC,CAAC,CAAC;YAGH,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACzC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE;oBAC7B,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;oBACzB,QAAQ,EAAE,MAAM,CAAC,YAAY;oBAC7B,MAAM,EAAE,MAAM,CAAC,QAAQ;oBACvB,SAAS;oBACT,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC;YAGD,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACvC,SAAS;gBACT,OAAO,EAAE,IAAI;gBACb,mBAAmB,EAAE,aAAa;gBAClC,qBAAqB,EAAE,cAAc;gBACrC,eAAe,EAAE,mBAAmB;gBACpC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,SAAS,OAAO,aAAa,IAAI,CAAC,CAAC;QAE/F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAEzE,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;gBACnB,OAAO,EAAE,UAAU,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC;gBAC/C,IAAI,EAAE,wBAAwB;gBAC9B,SAAS;gBACT,cAAc,EAAE,aAAa;gBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACvC,SAAS;gBACT,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,cAAc,EAAE,aAAa;gBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKK,AAAN,KAAK,CAAC,gBAAgB,CACL,IAAwB,EACpB,MAA2B;QAE9C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;gBAC3C,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;YAC1F,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC;QAE9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;CACF,CAAA;AA9SY,gDAAkB;AAE7B;IADC,IAAA,4BAAe,GAAE;8BACV,kBAAM;kDAAC;AAiET;IADL,IAAA,6BAAgB,EAAC,WAAW,CAAC;IAE3B,WAAA,IAAA,wBAAW,GAAE,CAAA;IACb,WAAA,IAAA,4BAAe,GAAE,CAAA;;;;yDA4CnB;AAGK;IADL,IAAA,6BAAgB,EAAC,cAAc,CAAC;IAE9B,WAAA,IAAA,wBAAW,GAAE,CAAA;IACb,WAAA,IAAA,4BAAe,GAAE,CAAA;;;;2DAiKnB;AAKK;IADL,IAAA,6BAAgB,EAAC,yBAAyB,CAAC;IAEzC,WAAA,IAAA,wBAAW,GAAE,CAAA;IACb,WAAA,IAAA,4BAAe,GAAE,CAAA;;;;0DAenB;6BA7SU,kBAAkB;IAnB9B,IAAA,6BAAgB,EAAC;QAChB,IAAI,EAAE;YACJ,MAAM,EAAE;gBACN,uBAAuB;gBACvB,uBAAuB;gBACvB,uBAAuB;gBACvB,uBAAuB;gBACvB,uBAAuB;gBACvB,uBAAuB;gBACvB,uBAAuB;gBACvB,uBAAuB;aACxB;YACD,WAAW,EAAE,IAAI;SAClB;QACD,SAAS,EAAE,cAAc;QACzB,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpC,WAAW,EAAE,KAAK;QAClB,YAAY,EAAE,KAAK;KACpB,CAAC;qCAQuC,wCAAkB;QAC1B,gBAAU;QACP,8BAAa;GATpC,kBAAkB,CA8S9B"}