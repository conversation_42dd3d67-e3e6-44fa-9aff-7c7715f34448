{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "ts-node -r tsconfig-paths/register src/main.ts", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@google-cloud/speech": "^7.2.0", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/platform-socket.io": "^11.1.5", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.5", "@types/nodemailer": "^6.4.17", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cors": "^2.8.5", "express": "^5.1.0", "ioredis": "^5.7.0", "multer": "^2.0.2", "nest-winston": "^1.10.2", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.3", "redis": "^4.7.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tsconfig-paths": "^4.2.0", "typeorm": "^0.3.25", "winston": "^3.17.0"}, "devDependencies": {"@nestjs/cli": "^11.0.10", "@nestjs/testing": "^11.1.5", "@types/bcryptjs": "^2.4.6", "@types/node": "^24.1.0", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}