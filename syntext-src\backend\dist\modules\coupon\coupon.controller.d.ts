import { CouponService } from './coupon.service';
import { CreateCouponDto, UpdateCouponDto, BatchCreateCouponDto, GenerateRandomCouponsDto } from './dto/coupon.dto';
export declare class CouponController {
    private readonly couponService;
    constructor(couponService: CouponService);
    validateCoupon(body: {
        code: string;
    }): Promise<{
        valid: boolean;
        coupon?: import("./entities/coupon.entity").Coupon;
        message?: string;
    }>;
    findAll(page?: number, limit?: number, status?: string): Promise<import("./entities/coupon.entity").Coupon[]>;
    create(createCouponDto: CreateCouponDto): Promise<import("./entities/coupon.entity").Coupon>;
    createBatch(batchCreateDto: BatchCreateCouponDto): Promise<import("./entities/coupon.entity").Coupon[]>;
    generateRandom(generateDto: GenerateRandomCouponsDto): Promise<import("./entities/coupon.entity").Coupon[]>;
    getStats(): Promise<import("./dto/coupon.dto").CouponStatsDto>;
    findOne(id: string): Promise<import("./entities/coupon.entity").Coupon>;
    update(id: string, updateCouponDto: UpdateCouponDto): Promise<import("./entities/coupon.entity").Coupon>;
    toggleStatus(id: string): Promise<import("./entities/coupon.entity").Coupon>;
    remove(id: string): Promise<void>;
}
