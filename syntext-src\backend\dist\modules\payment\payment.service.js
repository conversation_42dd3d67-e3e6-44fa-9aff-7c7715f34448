"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const payment_entity_1 = require("./entities/payment.entity");
const coupon_usage_entity_1 = require("./entities/coupon-usage.entity");
const coupon_service_1 = require("../coupon/coupon.service");
const subscription_service_1 = require("../subscription/subscription.service");
const user_service_1 = require("../user/user.service");
const alipay_processor_1 = require("./processors/alipay.processor");
const wechat_processor_1 = require("./processors/wechat.processor");
const stripe_processor_1 = require("./processors/stripe.processor");
let PaymentService = PaymentService_1 = class PaymentService {
    constructor(paymentRepository, couponUsageRepository, couponService, subscriptionService, userService, alipayProcessor, wechatProcessor, stripeProcessor) {
        this.paymentRepository = paymentRepository;
        this.couponUsageRepository = couponUsageRepository;
        this.couponService = couponService;
        this.subscriptionService = subscriptionService;
        this.userService = userService;
        this.alipayProcessor = alipayProcessor;
        this.wechatProcessor = wechatProcessor;
        this.stripeProcessor = stripeProcessor;
        this.logger = new common_1.Logger(PaymentService_1.name);
        this.processors = new Map([
            [payment_entity_1.PaymentMethod.ALIPAY, this.alipayProcessor],
            [payment_entity_1.PaymentMethod.WECHAT, this.wechatProcessor],
            [payment_entity_1.PaymentMethod.STRIPE, this.stripeProcessor],
        ]);
    }
    async getSubscriptionPlans() {
        return this.subscriptionService.getPlans();
    }
    async createPayment(userId, createPaymentDto) {
        try {
            this.logger.log(`Creating payment for user: ${userId}`);
            const plans = await this.subscriptionService.getPlans();
            const plan = plans.find(p => p.type === createPaymentDto.planType);
            if (!plan) {
                throw new common_1.BadRequestException('无效的订阅套餐');
            }
            const priceCalculation = await this.calculatePrice(createPaymentDto.planType, createPaymentDto.couponCode);
            const orderNumber = this.generateOrderNumber();
            const payment = this.paymentRepository.create({
                userId,
                paymentMethod: createPaymentDto.paymentMethod,
                orderNumber,
                amount: priceCalculation.finalPrice,
                originalAmount: priceCalculation.originalPrice,
                discountAmount: priceCalculation.discountAmount,
                couponCode: createPaymentDto.couponCode,
                currency: 'CNY',
                description: `订阅${plan.name}`,
                status: payment_entity_1.PaymentStatus.PENDING,
            });
            const savedPayment = await this.paymentRepository.save(payment);
            const processor = this.processors.get(createPaymentDto.paymentMethod);
            if (!processor) {
                throw new common_1.BadRequestException('不支持的支付方式');
            }
            const paymentResult = await processor.createPayment({
                orderNumber,
                amount: Math.round(priceCalculation.finalPrice * 100),
                currency: 'CNY',
                description: `订阅${plan.name}`,
                userId,
                notifyUrl: `${process.env.API_BASE_URL}/api/v1/payments/callback/${createPaymentDto.paymentMethod}`,
                returnUrl: createPaymentDto.returnUrl,
            });
            if (!paymentResult.success) {
                await this.paymentRepository.update(savedPayment.id, {
                    status: payment_entity_1.PaymentStatus.FAILED,
                    failureReason: paymentResult.error,
                });
                throw new common_1.BadRequestException(paymentResult.error);
            }
            await this.paymentRepository.update(savedPayment.id, {
                thirdPartyOrderId: paymentResult.thirdPartyOrderId,
                status: payment_entity_1.PaymentStatus.PROCESSING,
            });
            return {
                paymentId: savedPayment.id,
                orderNumber,
                paymentUrl: paymentResult.paymentUrl,
                paymentForm: paymentResult.paymentForm,
                amount: priceCalculation.finalPrice,
                currency: 'CNY',
                extraData: paymentResult.extraData,
            };
        }
        catch (error) {
            this.logger.error('Failed to create payment:', error);
            throw error;
        }
    }
    async handlePaymentCallback(method, callbackData) {
        try {
            this.logger.log(`Handling payment callback for method: ${method}`);
            const paymentMethod = method.toUpperCase();
            const processor = this.processors.get(paymentMethod);
            if (!processor) {
                throw new common_1.BadRequestException('不支持的支付方式');
            }
            const verificationResult = await processor.verifyCallback(callbackData);
            if (!verificationResult.verified) {
                this.logger.error('Payment callback verification failed:', verificationResult.error);
                return { success: false, message: '回调验证失败' };
            }
            const payment = await this.paymentRepository.findOne({
                where: { thirdPartyOrderId: verificationResult.thirdPartyOrderId },
            });
            if (!payment) {
                this.logger.error('Payment not found:', verificationResult.thirdPartyOrderId);
                return { success: false, message: '支付记录不存在' };
            }
            if (verificationResult.paymentStatus === 'success') {
                await this.paymentRepository.update(payment.id, {
                    status: payment_entity_1.PaymentStatus.SUCCESS,
                    paidAt: new Date(),
                    callbackData: JSON.stringify(verificationResult.rawData),
                });
                await this.handlePaymentSuccess(payment);
            }
            else if (verificationResult.paymentStatus === 'failed') {
                await this.paymentRepository.update(payment.id, {
                    status: payment_entity_1.PaymentStatus.FAILED,
                    failureReason: '支付失败',
                    callbackData: JSON.stringify(verificationResult.rawData),
                });
            }
            return { success: true, message: '回调处理成功' };
        }
        catch (error) {
            this.logger.error('Failed to handle payment callback:', error);
            return { success: false, message: '回调处理失败' };
        }
    }
    async validateCoupon(code) {
        return this.couponService.validateCoupon(code);
    }
    async calculatePrice(planType, couponCode) {
        const plans = await this.subscriptionService.getPlans();
        const plan = plans.find(p => p.type === planType);
        if (!plan) {
            throw new common_1.BadRequestException('无效的订阅套餐');
        }
        const originalPrice = plan.price;
        let discountAmount = 0;
        let couponInfo = undefined;
        if (couponCode) {
            const validation = await this.couponService.validateCoupon(couponCode);
            if (validation.valid && validation.coupon) {
                const coupon = validation.coupon;
                if (coupon.discountType === 'percentage') {
                    discountAmount = originalPrice * (coupon.discountValue / 100);
                }
                else if (coupon.discountType === 'fixed') {
                    discountAmount = Math.min(coupon.discountValue, originalPrice);
                }
                couponInfo = {
                    code: coupon.code,
                    name: coupon.name,
                    discountType: coupon.discountType,
                    discountValue: coupon.discountValue,
                };
            }
        }
        const finalPrice = Math.max(0, originalPrice - discountAmount);
        return {
            originalPrice,
            discountAmount,
            finalPrice,
            couponInfo,
        };
    }
    async getUserPayments(userId, page = 1, limit = 10) {
        const [payments, total] = await this.paymentRepository.findAndCount({
            where: { userId },
            order: { createdAt: 'DESC' },
            skip: (page - 1) * limit,
            take: limit,
        });
        return {
            payments,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    async getPaymentDetail(userId, paymentId) {
        const payment = await this.paymentRepository.findOne({
            where: { id: paymentId, userId },
        });
        if (!payment) {
            throw new common_1.NotFoundException('支付记录不存在');
        }
        return payment;
    }
    async cancelPayment(userId, paymentId) {
        const payment = await this.paymentRepository.findOne({
            where: { id: paymentId, userId },
        });
        if (!payment) {
            throw new common_1.NotFoundException('支付记录不存在');
        }
        if (payment.status !== payment_entity_1.PaymentStatus.PENDING && payment.status !== payment_entity_1.PaymentStatus.PROCESSING) {
            throw new common_1.BadRequestException('该订单无法取消');
        }
        await this.paymentRepository.update(paymentId, {
            status: payment_entity_1.PaymentStatus.CANCELLED,
        });
        return { message: '订单已取消' };
    }
    generateOrderNumber() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        return `PAY${timestamp}${random}`;
    }
    async getAllPayments(page = 1, limit = 10, status) {
        const where = {};
        if (status) {
            where.status = status;
        }
        const [payments, total] = await this.paymentRepository.findAndCount({
            where,
            order: { createdAt: 'DESC' },
            skip: (page - 1) * limit,
            take: limit,
            relations: ['user'],
        });
        return {
            payments,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    async getPaymentStats() {
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const totalOrders = await this.paymentRepository.count();
        const successOrders = await this.paymentRepository.count({
            where: { status: payment_entity_1.PaymentStatus.SUCCESS },
        });
        const failedOrders = await this.paymentRepository.count({
            where: { status: payment_entity_1.PaymentStatus.FAILED },
        });
        const pendingOrders = await this.paymentRepository.count({
            where: { status: payment_entity_1.PaymentStatus.PENDING },
        });
        const totalRevenueResult = await this.paymentRepository
            .createQueryBuilder('payment')
            .select('SUM(payment.amount)', 'total')
            .where('payment.status = :status', { status: payment_entity_1.PaymentStatus.SUCCESS })
            .getRawOne();
        const monthlyRevenueResult = await this.paymentRepository
            .createQueryBuilder('payment')
            .select('SUM(payment.amount)', 'total')
            .where('payment.status = :status', { status: payment_entity_1.PaymentStatus.SUCCESS })
            .andWhere('payment.paidAt >= :startOfMonth', { startOfMonth })
            .getRawOne();
        const dailyRevenueResult = await this.paymentRepository
            .createQueryBuilder('payment')
            .select('SUM(payment.amount)', 'total')
            .where('payment.status = :status', { status: payment_entity_1.PaymentStatus.SUCCESS })
            .andWhere('payment.paidAt >= :startOfDay', { startOfDay })
            .getRawOne();
        const paymentMethodStats = await this.paymentRepository
            .createQueryBuilder('payment')
            .select('payment.paymentMethod', 'method')
            .addSelect('COUNT(*)', 'count')
            .addSelect('SUM(payment.amount)', 'revenue')
            .where('payment.status = :status', { status: payment_entity_1.PaymentStatus.SUCCESS })
            .groupBy('payment.paymentMethod')
            .getRawMany();
        return {
            totalOrders,
            successOrders,
            failedOrders,
            pendingOrders,
            totalRevenue: parseFloat(totalRevenueResult?.total || '0'),
            monthlyRevenue: parseFloat(monthlyRevenueResult?.total || '0'),
            dailyRevenue: parseFloat(dailyRevenueResult?.total || '0'),
            paymentMethodStats: paymentMethodStats.map(stat => ({
                method: stat.method,
                count: parseInt(stat.count),
                revenue: parseFloat(stat.revenue || '0'),
            })),
        };
    }
    async processRefund(paymentId, reason, amount) {
        const payment = await this.paymentRepository.findOne({
            where: { id: paymentId },
        });
        if (!payment) {
            throw new common_1.NotFoundException('支付记录不存在');
        }
        if (payment.status !== payment_entity_1.PaymentStatus.SUCCESS) {
            throw new common_1.BadRequestException('只能对成功的支付进行退款');
        }
        const refundAmount = amount || payment.amount;
        if (refundAmount > payment.amount) {
            throw new common_1.BadRequestException('退款金额不能超过支付金额');
        }
        const processor = this.processors.get(payment.paymentMethod);
        if (!processor) {
            throw new common_1.BadRequestException('不支持的支付方式');
        }
        const refundOrderNumber = `REF${Date.now()}${Math.floor(Math.random() * 1000)}`;
        const refundResult = await processor.refund({
            thirdPartyOrderId: payment.thirdPartyOrderId,
            refundAmount: Math.round(refundAmount * 100),
            reason,
            refundOrderNumber,
        });
        if (!refundResult.success) {
            throw new common_1.BadRequestException(refundResult.error || '退款失败');
        }
        await this.paymentRepository.update(paymentId, {
            status: payment_entity_1.PaymentStatus.REFUNDED,
            failureReason: `退款原因: ${reason}`,
        });
        return {
            message: '退款成功',
            refundOrderNumber,
            refundAmount,
        };
    }
    async handlePaymentSuccess(payment) {
        try {
            this.logger.log(`Payment success handled for order: ${payment.orderNumber}`);
        }
        catch (error) {
            this.logger.error('Failed to handle payment success:', error);
        }
    }
};
exports.PaymentService = PaymentService;
exports.PaymentService = PaymentService = PaymentService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(payment_entity_1.Payment)),
    __param(1, (0, typeorm_1.InjectRepository)(coupon_usage_entity_1.CouponUsage)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        coupon_service_1.CouponService,
        subscription_service_1.SubscriptionService,
        user_service_1.UserService,
        alipay_processor_1.AlipayProcessor,
        wechat_processor_1.WechatProcessor,
        stripe_processor_1.StripeProcessor])
], PaymentService);
//# sourceMappingURL=payment.service.js.map