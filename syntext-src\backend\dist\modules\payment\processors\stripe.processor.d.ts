import { PaymentMethod } from '../entities/payment.entity';
import { PaymentProcessor, CreatePaymentParams, CreatePaymentResult, CallbackVerificationResult, PaymentStatusResult, RefundParams, RefundResult } from '../interfaces/payment-processor.interface';
export declare class StripeProcessor implements PaymentProcessor {
    private readonly logger;
    readonly paymentMethod = PaymentMethod.STRIPE;
    private readonly config;
    createPayment(params: CreatePaymentParams): Promise<CreatePaymentResult>;
    verifyCallback(callbackData: any): Promise<CallbackVerificationResult>;
    queryPaymentStatus(thirdPartyOrderId: string): Promise<PaymentStatusResult>;
    refund(params: RefundParams): Promise<RefundResult>;
    private isProductionConfigured;
    private createRealPayment;
    private createMockPayment;
    private verifyRealCallback;
    private verifyMockCallback;
    private queryRealPaymentStatus;
    private queryMockPaymentStatus;
    private processRealRefund;
    private processMockRefund;
}
