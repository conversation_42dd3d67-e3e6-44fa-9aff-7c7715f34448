import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, Length, IsOptional } from 'class-validator';

export class VerifyCodeDto {
  @ApiProperty({ description: '邮箱地址', example: '<EMAIL>' })
  @IsEmail({}, { message: '请输入有效的邮箱地址' })
  email: string;

  @ApiProperty({ description: '验证码', example: '123456' })
  @IsString()
  @Length(6, 6, { message: '验证码必须是6位数字' })
  code: string;

  @ApiProperty({ description: '内测验证码', example: 'weilynCHENliuYU', required: false })
  @IsOptional()
  @IsString()
  betaCode?: string;
}
