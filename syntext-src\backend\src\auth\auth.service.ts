import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UserService } from '../modules/user/user.service';
import { EmailService } from './email.service';
import { User } from '../modules/user/entities/user.entity';
import { SendCodeDto } from './dto/send-code.dto';
import { VerifyCodeDto } from './dto/verify-code.dto';

@Injectable()
export class AuthService {
  private verificationCodes = new Map<string, { code: string; expiresAt: Date }>();

  constructor(
    private userService: UserService,
    private jwtService: JwtService,
    private emailService: EmailService,
    private configService: ConfigService,
  ) {}

  async sendVerificationCode(sendCodeDto: SendCodeDto): Promise<{ message: string }> {
    const { email } = sendCodeDto;
    
    // 检查发送频率限制
    const existingCode = this.verificationCodes.get(email);
    if (existingCode && existingCode.expiresAt > new Date()) {
      throw new BadRequestException('验证码发送过于频繁，请稍后再试');
    }

    // 生成6位数验证码
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5分钟过期

    // 存储验证码
    this.verificationCodes.set(email, { code, expiresAt });

    // 发送邮件
    await this.emailService.sendVerificationCode(email, code);

    return { message: '验证码已发送到您的邮箱' };
  }

  async verifyCodeAndLogin(verifyCodeDto: VerifyCodeDto): Promise<{
    access_token: string;
    user: User;
  }> {
    const { email, code, betaCode } = verifyCodeDto;

    // 内测验证码验证
    const isBetaMode = this.configService.get<boolean>('BETA_MODE', false);
    if (isBetaMode) {
      const expectedBetaCode = this.configService.get<string>('BETA_CODE', 'weilynCHENliuYU');
      if (!betaCode || betaCode !== expectedBetaCode) {
        throw new UnauthorizedException('内测验证码错误，请联系管理员获取正确的验证码');
      }
    }

    // 验证邮箱验证码
    const storedCode = this.verificationCodes.get(email);
    if (!storedCode) {
      throw new UnauthorizedException('验证码不存在或已过期');
    }

    if (storedCode.expiresAt < new Date()) {
      this.verificationCodes.delete(email);
      throw new UnauthorizedException('验证码已过期');
    }

    if (storedCode.code !== code) {
      throw new UnauthorizedException('验证码错误');
    }

    // 删除已使用的验证码
    this.verificationCodes.delete(email);

    // 查找或创建用户
    let user = await this.userService.findByEmail(email);
    if (!user) {
      user = await this.userService.create({ email });
    }

    // 更新最后登录时间
    await this.userService.updateLastLogin(user.id);

    // 生成JWT token
    const payload = { sub: user.id, email: user.email, role: user.role };
    const access_token = this.jwtService.sign(payload);

    return {
      access_token,
      user,
    };
  }

  async refreshToken(user: User): Promise<{ access_token: string }> {
    const payload = { sub: user.id, email: user.email, role: user.role };
    const access_token = this.jwtService.sign(payload);

    return { access_token };
  }

  async getProfile(userId: string): Promise<User> {
    return await this.userService.findOne(userId);
  }
}
