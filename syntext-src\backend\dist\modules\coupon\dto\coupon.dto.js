"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CouponStatsDto = exports.GenerateRandomCouponsDto = exports.BatchCreateCouponDto = exports.UpdateCouponDto = exports.CreateCouponDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const coupon_entity_1 = require("../entities/coupon.entity");
class CreateCouponDto {
}
exports.CreateCouponDto = CreateCouponDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCouponDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码名称' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCouponDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码描述', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCouponDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣类型', enum: coupon_entity_1.DiscountType }),
    (0, class_validator_1.IsEnum)(coupon_entity_1.DiscountType),
    __metadata("design:type", String)
], CreateCouponDto.prototype, "discountType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣值' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateCouponDto.prototype, "discountValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '使用限制次数', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateCouponDto.prototype, "usageLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最小订单金额', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateCouponDto.prototype, "minOrderAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '过期时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], CreateCouponDto.prototype, "expiresAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码状态', enum: coupon_entity_1.CouponStatus, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(coupon_entity_1.CouponStatus),
    __metadata("design:type", String)
], CreateCouponDto.prototype, "status", void 0);
class UpdateCouponDto {
}
exports.UpdateCouponDto = UpdateCouponDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateCouponDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码名称', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateCouponDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码描述', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateCouponDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣类型', enum: coupon_entity_1.DiscountType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(coupon_entity_1.DiscountType),
    __metadata("design:type", String)
], UpdateCouponDto.prototype, "discountType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣值', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], UpdateCouponDto.prototype, "discountValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '使用限制次数', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], UpdateCouponDto.prototype, "usageLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最小订单金额', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UpdateCouponDto.prototype, "minOrderAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '过期时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], UpdateCouponDto.prototype, "expiresAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码状态', enum: coupon_entity_1.CouponStatus, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(coupon_entity_1.CouponStatus),
    __metadata("design:type", String)
], UpdateCouponDto.prototype, "status", void 0);
class BatchCreateCouponDto {
}
exports.BatchCreateCouponDto = BatchCreateCouponDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建数量' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], BatchCreateCouponDto.prototype, "count", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码名称前缀' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], BatchCreateCouponDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码描述', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BatchCreateCouponDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣类型', enum: coupon_entity_1.DiscountType }),
    (0, class_validator_1.IsEnum)(coupon_entity_1.DiscountType),
    __metadata("design:type", String)
], BatchCreateCouponDto.prototype, "discountType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣值' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], BatchCreateCouponDto.prototype, "discountValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '使用限制次数', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], BatchCreateCouponDto.prototype, "usageLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最小订单金额', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], BatchCreateCouponDto.prototype, "minOrderAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '过期时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], BatchCreateCouponDto.prototype, "expiresAt", void 0);
class GenerateRandomCouponsDto {
}
exports.GenerateRandomCouponsDto = GenerateRandomCouponsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '生成数量' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(50),
    __metadata("design:type", Number)
], GenerateRandomCouponsDto.prototype, "count", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣类型', enum: coupon_entity_1.DiscountType }),
    (0, class_validator_1.IsEnum)(coupon_entity_1.DiscountType),
    __metadata("design:type", String)
], GenerateRandomCouponsDto.prototype, "discountType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣值' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], GenerateRandomCouponsDto.prototype, "discountValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码名称前缀', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenerateRandomCouponsDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '优惠码描述', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenerateRandomCouponsDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '使用限制次数', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], GenerateRandomCouponsDto.prototype, "usageLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最小订单金额', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], GenerateRandomCouponsDto.prototype, "minOrderAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '过期时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], GenerateRandomCouponsDto.prototype, "expiresAt", void 0);
class CouponStatsDto {
}
exports.CouponStatsDto = CouponStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总数量' }),
    __metadata("design:type", Number)
], CouponStatsDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活跃数量' }),
    __metadata("design:type", Number)
], CouponStatsDto.prototype, "active", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '非活跃数量' }),
    __metadata("design:type", Number)
], CouponStatsDto.prototype, "inactive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '过期数量' }),
    __metadata("design:type", Number)
], CouponStatsDto.prototype, "expired", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总使用次数' }),
    __metadata("design:type", Number)
], CouponStatsDto.prototype, "totalUsed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '平均使用次数' }),
    __metadata("design:type", Number)
], CouponStatsDto.prototype, "avgUsed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '折扣类型统计' }),
    __metadata("design:type", Array)
], CouponStatsDto.prototype, "discountTypeStats", void 0);
//# sourceMappingURL=coupon.dto.js.map