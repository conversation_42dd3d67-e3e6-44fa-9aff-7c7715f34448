import { ConfigService } from '@nestjs/config';
export interface SpeechToTextResult {
    text: string;
    confidence: number;
    language: string;
}
export declare class SpeechService {
    private configService;
    private readonly logger;
    private speechClient;
    constructor(configService: ConfigService);
    private initializeSpeechClient;
    speechToText(audioData: Buffer, language?: string): Promise<SpeechToTextResult>;
    private deepseekSpeechToText;
    private analyzeAudioFeatures;
    private calculateAverageAmplitude;
    private calculateEnergyLevel;
    private detectSilencePeriods;
    private calculateComplexity;
    private generateTranscriptionFromFeatures;
    private generateRuleBasedTranscription;
    private calculateConfidence;
    private mockSpeechToText;
    detectLanguage(audioData: Buffer): Promise<string>;
    streamingSpeechToText(audioChunk: Buffer, language: string): Promise<SpeechToTextResult | null>;
}
