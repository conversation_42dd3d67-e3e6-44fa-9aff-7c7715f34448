import { Repository } from 'typeorm';
import { Payment } from './entities/payment.entity';
import { CouponUsage } from './entities/coupon-usage.entity';
import { CouponService } from '../coupon/coupon.service';
import { SubscriptionService } from '../subscription/subscription.service';
import { UserService } from '../user/user.service';
import { CreatePaymentDto, PriceCalculationDto, PaymentStatsDto } from './dto/payment.dto';
import { PlanType } from '../subscription/entities/subscription.entity';
import { AlipayProcessor } from './processors/alipay.processor';
import { WechatProcessor } from './processors/wechat.processor';
import { StripeProcessor } from './processors/stripe.processor';
export declare class PaymentService {
    private paymentRepository;
    private couponUsageRepository;
    private couponService;
    private subscriptionService;
    private userService;
    private alipayProcessor;
    private wechatProcessor;
    private stripeProcessor;
    private readonly logger;
    private readonly processors;
    constructor(paymentRepository: Repository<Payment>, couponUsageRepository: Repository<CouponUsage>, couponService: CouponService, subscriptionService: SubscriptionService, userService: UserService, alipayProcessor: AlipayProcessor, wechatProcessor: WechatProcessor, stripeProcessor: StripeProcessor);
    getSubscriptionPlans(): Promise<{
        type: PlanType;
        name: string;
        price: number;
        translationLimit: number;
        features: string[];
    }[]>;
    createPayment(userId: string, createPaymentDto: CreatePaymentDto): Promise<{
        paymentId: string;
        orderNumber: string;
        paymentUrl: string;
        paymentForm: string;
        amount: number;
        currency: string;
        extraData: Record<string, any>;
    }>;
    handlePaymentCallback(method: string, callbackData: any): Promise<{
        success: boolean;
        message: string;
    }>;
    validateCoupon(code: string): Promise<{
        valid: boolean;
        coupon?: import("../coupon/entities/coupon.entity").Coupon;
        message?: string;
    }>;
    calculatePrice(planType: string, couponCode?: string): Promise<PriceCalculationDto>;
    getUserPayments(userId: string, page?: number, limit?: number): Promise<{
        payments: Payment[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getPaymentDetail(userId: string, paymentId: string): Promise<Payment>;
    cancelPayment(userId: string, paymentId: string): Promise<{
        message: string;
    }>;
    private generateOrderNumber;
    getAllPayments(page?: number, limit?: number, status?: string): Promise<{
        payments: Payment[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getPaymentStats(): Promise<PaymentStatsDto>;
    processRefund(paymentId: string, reason: string, amount?: number): Promise<{
        message: string;
        refundOrderNumber: string;
        refundAmount: number;
    }>;
    private handlePaymentSuccess;
}
